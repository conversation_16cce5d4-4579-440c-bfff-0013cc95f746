import 'package:cloud_firestore/cloud_firestore.dart';

enum PaymentStatus { pending, completed, failed, refunded }

extension PaymentStatusExtension on PaymentStatus {
  String get name {
    switch (this) {
      case PaymentStatus.pending:
        return 'pending';
      case PaymentStatus.completed:
        return 'completed';
      case PaymentStatus.failed:
        return 'failed';
      case PaymentStatus.refunded:
        return 'refunded';
    }
  }

  static PaymentStatus fromString(String? value) {
    switch (value) {
      case 'completed':
        return PaymentStatus.completed;
      case 'failed':
        return PaymentStatus.failed;
      case 'refunded':
        return PaymentStatus.refunded;
      default:
        return PaymentStatus.pending;
    }
  }

  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return 'Đang <PERSON>';
      case PaymentStatus.completed:
        return 'Thành Công';
      case PaymentStatus.failed:
        return 'Thất Bại';
      case PaymentStatus.refunded:
        return 'Đã Hoàn Tiền';
    }
  }
}

enum PaymentMethod { creditCard, debitCard, momo, zalopay, vnpay, cash }

extension PaymentMethodExtension on PaymentMethod {
  String get name {
    switch (this) {
      case PaymentMethod.creditCard:
        return 'credit_card';
      case PaymentMethod.debitCard:
        return 'debit_card';
      case PaymentMethod.momo:
        return 'momo';
      case PaymentMethod.zalopay:
        return 'zalopay';
      case PaymentMethod.vnpay:
        return 'vnpay';
      case PaymentMethod.cash:
        return 'cash';
    }
  }

  static PaymentMethod fromString(String? value) {
    switch (value) {
      case 'credit_card':
        return PaymentMethod.creditCard;
      case 'debit_card':
        return PaymentMethod.debitCard;
      case 'momo':
        return PaymentMethod.momo;
      case 'zalopay':
        return PaymentMethod.zalopay;
      case 'vnpay':
        return PaymentMethod.vnpay;
      case 'cash':
        return PaymentMethod.cash;
      default:
        return PaymentMethod.cash;
    }
  }

  String get displayName {
    switch (this) {
      case PaymentMethod.creditCard:
        return 'Thẻ Tín Dụng';
      case PaymentMethod.debitCard:
        return 'Thẻ Ghi Nợ';
      case PaymentMethod.momo:
        return 'MoMo';
      case PaymentMethod.zalopay:
        return 'ZaloPay';
      case PaymentMethod.vnpay:
        return 'VNPay';
      case PaymentMethod.cash:
        return 'Tiền Mặt';
    }
  }

  String get provider {
    switch (this) {
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return 'visa'; // or mastercard
      case PaymentMethod.momo:
        return 'momo';
      case PaymentMethod.zalopay:
        return 'zalopay';
      case PaymentMethod.vnpay:
        return 'vnpay';
      case PaymentMethod.cash:
        return 'cash';
    }
  }
}

class PaymentModel {
  final String id;
  final String userId;
  final String ticketId;
  final double amount;
  final String currency; // "VND"
  final PaymentMethod method;
  final String provider; // "visa", "mastercard", "momo", "zalopay"
  final String? transactionId; // ID từ payment gateway
  final PaymentStatus status;
  final Map<String, dynamic>? gatewayResponse; // response từ payment gateway
  final DateTime createdAt;
  final DateTime? completedAt;
  final DateTime? refundedAt;

  PaymentModel({
    required this.id,
    required this.userId,
    required this.ticketId,
    required this.amount,
    this.currency = 'VND',
    required this.method,
    required this.provider,
    this.transactionId,
    this.status = PaymentStatus.pending,
    this.gatewayResponse,
    required this.createdAt,
    this.completedAt,
    this.refundedAt,
  });

  factory PaymentModel.fromJson(Map<String, dynamic> json) {
    return PaymentModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      ticketId: json['ticketId'] ?? '',
      amount: json['amount']?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'VND',
      method: PaymentMethodExtension.fromString(json['method']),
      provider: json['provider'] ?? '',
      transactionId: json['transactionId'],
      status: PaymentStatusExtension.fromString(json['status']),
      gatewayResponse: json['gatewayResponse'],
      createdAt: json['createdAt'] != null 
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      completedAt: json['completedAt'] != null 
          ? (json['completedAt'] as Timestamp).toDate()
          : null,
      refundedAt: json['refundedAt'] != null 
          ? (json['refundedAt'] as Timestamp).toDate()
          : null,
    );
  }

  factory PaymentModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PaymentModel.fromJson({...data, 'id': doc.id});
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'ticketId': ticketId,
      'amount': amount,
      'currency': currency,
      'method': method.name,
      'provider': provider,
      'transactionId': transactionId,
      'status': status.name,
      'gatewayResponse': gatewayResponse,
      'createdAt': Timestamp.fromDate(createdAt),
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'refundedAt': refundedAt != null ? Timestamp.fromDate(refundedAt!) : null,
    };
  }

  Map<String, dynamic> toFirestore() => toJson();

  PaymentModel copyWith({
    String? id,
    String? userId,
    String? ticketId,
    double? amount,
    String? currency,
    PaymentMethod? method,
    String? provider,
    String? transactionId,
    PaymentStatus? status,
    Map<String, dynamic>? gatewayResponse,
    DateTime? createdAt,
    DateTime? completedAt,
    DateTime? refundedAt,
  }) {
    return PaymentModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      ticketId: ticketId ?? this.ticketId,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      method: method ?? this.method,
      provider: provider ?? this.provider,
      transactionId: transactionId ?? this.transactionId,
      status: status ?? this.status,
      gatewayResponse: gatewayResponse ?? this.gatewayResponse,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      refundedAt: refundedAt ?? this.refundedAt,
    );
  }

  // Helper methods
  bool get isPending => status == PaymentStatus.pending;
  bool get isCompleted => status == PaymentStatus.completed;
  bool get isFailed => status == PaymentStatus.failed;
  bool get isRefunded => status == PaymentStatus.refunded;

  bool get isSuccessful => status == PaymentStatus.completed;
  bool get canBeRefunded => status == PaymentStatus.completed && refundedAt == null;

  String get displayAmount => '${amount.toStringAsFixed(0)} $currency';
  String get displayStatus => status.displayName;
  String get displayMethod => method.displayName;

  Duration? get processingTime {
    if (completedAt != null) {
      return completedAt!.difference(createdAt);
    }
    return null;
  }

  String get paymentReference => transactionId ?? id;

  // Validation methods
  bool get isValidAmount => amount > 0;
  bool get hasTransactionId => transactionId != null && transactionId!.isNotEmpty;
  bool get hasGatewayResponse => gatewayResponse != null && gatewayResponse!.isNotEmpty;
}
