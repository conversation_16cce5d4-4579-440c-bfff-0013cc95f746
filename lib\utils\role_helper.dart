import 'package:get/get.dart';
import '../controllers/auth_controller.dart';

/// Helper class để kiểm tra vai trò người dùng
class RoleHelper {
  /// Kiểm tra xem người dùng hiện tại có phải là admin không
  static bool isAdmin() {
    final authController = Get.find<AuthController>();
    
    // Kiểm tra xem người dùng đã đăng nhập chưa
    if (!authController.isLoggedIn) {
      return false;
    }
    
    // Kiểm tra vai trò của người dùng
    final userRole = authController.userRole;
    return userRole == 'admin' || userRole == 'developer';
  }
  
  /// Kiểm tra xem người dùng hiện tại có phải là developer không
  static bool isDeveloper() {
    final authController = Get.find<AuthController>();
    
    // Kiểm tra xem người dùng đã đăng nhập chưa
    if (!authController.isLoggedIn) {
      return false;
    }
    
    // Kiểm tra vai trò của người dùng
    final userRole = authController.userRole;
    return userRole == 'developer';
  }
  
  /// Kiểm tra xem người dùng hiện tại có quyền quản trị không (admin hoặc developer)
  static bool hasAdminAccess() {
    return isAdmin() || isDeveloper();
  }
}
