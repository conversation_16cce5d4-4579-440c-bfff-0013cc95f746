import 'package:get/get.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';
import 'auth_controller.dart';

class NotificationController extends GetxController {
  final NotificationService _notificationService = NotificationService();
  final AuthController _authController = Get.find<AuthController>();

  // Danh sách thông báo
  final RxList<UserNotificationViewModel> _notifications =
      <UserNotificationViewModel>[].obs;

  // Số lượng thông báo chưa đọc
  final RxInt _unreadCount = 0.obs;

  // Trạng thái loading
  final RxBool _isLoading = false.obs;

  // Thông báo lỗi
  final RxString _errorMessage = ''.obs;

  // Getters
  List<UserNotificationViewModel> get notifications => _notifications;
  int get unreadCount => _unreadCount.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;

  // Getter cho các thông báo đã lọc
  List<UserNotificationViewModel> get unreadNotifications =>
      _notifications.where((n) => !n.userNotification.isRead).toList();

  List<UserNotificationViewModel> get unseenNotifications =>
      _notifications.where((n) => !n.userNotification.isSeen).toList();

  @override
  void onInit() {
    super.onInit();

    // Lắng nghe sự thay đổi của người dùng đăng nhập
    ever(_authController.isLoggedInObs, (_) {
      if (_authController.isLoggedIn) {
        fetchNotifications();
      } else {
        _notifications.clear();
        _unreadCount.value = 0;
      }
    });

    // Nếu người dùng đã đăng nhập, lấy thông báo
    if (_authController.isLoggedIn) {
      fetchNotifications();
    }
  }

  // Lấy tất cả thông báo
  Future<void> fetchNotifications() async {
    if (!_authController.isLoggedIn) {
      print('NotificationController: User not logged in');
      return;
    }

    final userId = _authController.user?.id;
    if (userId == null) {
      print('NotificationController: User ID is null');
      return;
    }

    print('NotificationController: Fetching notifications for user $userId');
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // Lấy tất cả thông báo công khai
      final publicNotifications =
          await _notificationService.getPublicNotifications();
      print(
          'NotificationController: Found ${publicNotifications.length} public notifications');

      // Lấy thông báo dành riêng cho người dùng
      final userTargetedNotifications =
          await _notificationService.getUserTargetedNotifications(userId);
      print(
          'NotificationController: Found ${userTargetedNotifications.length} user-targeted notifications');

      // Kết hợp tất cả thông báo
      final allNotifications = [
        ...publicNotifications,
        ...userTargetedNotifications
      ];
      print(
          'NotificationController: Total notifications: ${allNotifications.length}');

      // Lấy trạng thái thông báo của người dùng
      final userNotificationStates =
          await _notificationService.getUserNotificationStates(userId);
      print(
          'NotificationController: Found ${userNotificationStates.length} user notification states');

      // Map để lưu trữ trạng thái thông báo theo notificationId
      final userNotificationMap = <String, UserNotificationModel>{};
      for (final state in userNotificationStates) {
        userNotificationMap[state.notificationId] = state;
      }

      // Tạo danh sách UserNotificationViewModel
      final notificationViewModels = <UserNotificationViewModel>[];

      for (final notification in allNotifications) {
        print(
            'NotificationController: Processing notification ${notification.id}: ${notification.title}');
        // Kiểm tra xem đã có trạng thái thông báo chưa
        UserNotificationModel userNotification;

        if (userNotificationMap.containsKey(notification.id)) {
          // Nếu đã có trạng thái và chưa bị xóa
          final state = userNotificationMap[notification.id]!;
          if (!state.isDeleted) {
            print(
                'NotificationController: Using existing state for notification ${notification.id}');
            userNotification = state;
            notificationViewModels.add(UserNotificationViewModel(
              notification: notification,
              userNotification: userNotification,
            ));
          } else {
            print(
                'NotificationController: Notification ${notification.id} is deleted, skipping');
          }
        } else {
          // Nếu chưa có trạng thái, tạo mới
          print(
              'NotificationController: Creating new state for notification ${notification.id}');
          try {
            userNotification =
                await _notificationService.createOrUpdateUserNotification(
                      userId: userId,
                      notificationId: notification.id,
                    ) ??
                    UserNotificationModel(
                      id: '',
                      userId: userId,
                      notificationId: notification.id,
                      createdAt: DateTime.now(),
                    );

            notificationViewModels.add(UserNotificationViewModel(
              notification: notification,
              userNotification: userNotification,
            ));
          } catch (e) {
            print(
                'NotificationController: Error creating user notification state: $e');
          }
        }
      }

      // Sắp xếp thông báo theo thời gian tạo (mới nhất lên đầu)
      notificationViewModels.sort((a, b) =>
          b.notification.createdAt.compareTo(a.notification.createdAt));

      // Cập nhật danh sách thông báo
      _notifications.value = notificationViewModels;
      print(
          'NotificationController: Final notification count: ${notificationViewModels.length}');

      // Cập nhật số lượng thông báo chưa đọc
      _unreadCount.value = notificationViewModels
          .where((n) => !n.userNotification.isRead)
          .length;
      print('NotificationController: Unread count: ${_unreadCount.value}');
    } catch (e) {
      print('NotificationController: Error fetching notifications: $e');
      _errorMessage.value = 'Không thể lấy thông báo: $e';
    } finally {
      _isLoading.value = false;
    }
  }

  // Đánh dấu thông báo là đã đọc
  Future<bool> markAsRead(String notificationId) async {
    if (!_authController.isLoggedIn) return false;

    final userId = _authController.user?.id;
    if (userId == null) return false;

    try {
      final result = await _notificationService.createOrUpdateUserNotification(
        userId: userId,
        notificationId: notificationId,
        isRead: true,
        isSeen: true,
      );

      if (result != null) {
        // Cập nhật trạng thái trong danh sách
        final index = _notifications
            .indexWhere((n) => n.notification.id == notificationId);

        if (index >= 0) {
          final notification = _notifications[index];
          _notifications[index] = UserNotificationViewModel(
            notification: notification.notification,
            userNotification: result,
          );

          // Cập nhật số lượng thông báo chưa đọc
          _unreadCount.value =
              _notifications.where((n) => !n.userNotification.isRead).length;
        }

        return true;
      }

      return false;
    } catch (e) {
      _errorMessage.value = 'Không thể đánh dấu thông báo là đã đọc: $e';
      return false;
    }
  }

  // Đánh dấu thông báo là đã xem
  Future<bool> markAsSeen(String notificationId) async {
    if (!_authController.isLoggedIn) return false;

    final userId = _authController.user?.id;
    if (userId == null) return false;

    try {
      final result = await _notificationService.createOrUpdateUserNotification(
        userId: userId,
        notificationId: notificationId,
        isSeen: true,
      );

      if (result != null) {
        // Cập nhật trạng thái trong danh sách
        final index = _notifications
            .indexWhere((n) => n.notification.id == notificationId);

        if (index >= 0) {
          final notification = _notifications[index];
          _notifications[index] = UserNotificationViewModel(
            notification: notification.notification,
            userNotification: result,
          );
        }

        return true;
      }

      return false;
    } catch (e) {
      _errorMessage.value = 'Không thể đánh dấu thông báo là đã xem: $e';
      return false;
    }
  }

  // Đánh dấu thông báo là đã xóa
  Future<bool> markAsDeleted(String notificationId) async {
    if (!_authController.isLoggedIn) return false;

    final userId = _authController.user?.id;
    if (userId == null) return false;

    try {
      final result = await _notificationService.createOrUpdateUserNotification(
        userId: userId,
        notificationId: notificationId,
        isDeleted: true,
      );

      if (result != null) {
        // Xóa thông báo khỏi danh sách
        _notifications.removeWhere((n) => n.notification.id == notificationId);

        // Cập nhật số lượng thông báo chưa đọc
        _unreadCount.value =
            _notifications.where((n) => !n.userNotification.isRead).length;

        return true;
      }

      return false;
    } catch (e) {
      _errorMessage.value = 'Không thể xóa thông báo: $e';
      return false;
    }
  }

  // Đánh dấu tất cả thông báo là đã đọc
  Future<bool> markAllAsRead() async {
    if (!_authController.isLoggedIn) return false;

    final userId = _authController.user?.id;
    if (userId == null) return false;

    try {
      final result = await _notificationService.markAllAsRead(userId);

      if (result) {
        // Cập nhật trạng thái trong danh sách
        final updatedNotifications = _notifications.map((notification) {
          final updatedUserNotification =
              notification.userNotification.copyWith(
            isRead: true,
            isSeen: true,
            readAt: DateTime.now(),
            seenAt: DateTime.now(),
          );

          return UserNotificationViewModel(
            notification: notification.notification,
            userNotification: updatedUserNotification,
          );
        }).toList();

        _notifications.value = updatedNotifications;
        _unreadCount.value = 0;

        return true;
      }

      return false;
    } catch (e) {
      _errorMessage.value = 'Không thể đánh dấu tất cả thông báo là đã đọc: $e';
      return false;
    }
  }
}
