import 'dart:async';
import 'package:get/get.dart';
import '../models/realtime_notification_model.dart';
import '../services/realtime_database_service.dart';
import 'auth_controller.dart';

class RealtimeNotificationController extends GetxController {
  final RealtimeDatabaseService _realtimeService = RealtimeDatabaseService();
  final AuthController _authController = Get.find<AuthController>();

  // Observables
  final RxList<RealtimeNotificationModel> _notifications = <RealtimeNotificationModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxInt _unreadCount = 0.obs;
  final RxString _errorMessage = ''.obs;

  // Streams
  StreamSubscription? _publicNotificationsSubscription;
  StreamSubscription? _userNotificationsSubscription;

  // Getters
  List<RealtimeNotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading.value;
  int get unreadCount => _unreadCount.value;
  String get errorMessage => _errorMessage.value;

  @override
  void onInit() {
    super.onInit();
    _setupAuthListener();
  }

  @override
  void onClose() {
    _cancelSubscriptions();
    super.onClose();
  }

  // Thiết lập lắng nghe sự thay đổi đăng nhập
  void _setupAuthListener() {
    ever(_authController.userRx, (_) {
      _cancelSubscriptions();
      if (_authController.user != null) {
        _fetchNotifications();
      } else {
        _notifications.clear();
        _unreadCount.value = 0;
      }
    });

    // Fetch ngay lập tức nếu đã đăng nhập
    if (_authController.user != null) {
      _fetchNotifications();
    }
  }

  // Hủy các subscription
  void _cancelSubscriptions() {
    _publicNotificationsSubscription?.cancel();
    _userNotificationsSubscription?.cancel();
    _publicNotificationsSubscription = null;
    _userNotificationsSubscription = null;
  }

  // Lấy thông báo
  void _fetchNotifications() {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // Lấy thông báo công khai
      _publicNotificationsSubscription = _realtimeService
          .getPublicNotificationsStream()
          .listen(_handlePublicNotifications, onError: _handleError);

      // Lấy thông báo của người dùng
      if (_authController.user?.id != null) {
        _userNotificationsSubscription = _realtimeService
            .getUserNotificationsStream(_authController.user!.id!)
            .listen(_handleUserNotifications, onError: _handleError);
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tải thông báo: $e';
      _isLoading.value = false;
    }
  }

  // Xử lý thông báo công khai
  void _handlePublicNotifications(List<RealtimeNotificationModel> publicNotifications) {
    print('Received ${publicNotifications.length} public notifications');
    
    // Lọc thông báo admin nếu không phải admin
    final filteredPublicNotifications = publicNotifications.where((notification) {
      final isAdminNotification = notification.data?['isAdminNotification'] == 'true';
      return !isAdminNotification || _authController.isAdmin;
    }).toList();

    // Cập nhật danh sách thông báo
    _updateNotifications(filteredPublicNotifications, isPublic: true);
  }

  // Xử lý thông báo của người dùng
  void _handleUserNotifications(List<RealtimeNotificationModel> userNotifications) {
    print('Received ${userNotifications.length} user notifications');
    _updateNotifications(userNotifications, isPublic: false);
  }

  // Cập nhật danh sách thông báo
  void _updateNotifications(List<RealtimeNotificationModel> newNotifications, {required bool isPublic}) {
    // Nếu là thông báo công khai, xóa các thông báo công khai cũ
    if (isPublic) {
      _notifications.removeWhere((notification) => 
        notification.isPublic && notification.targetUserIds == null);
    } else {
      // Nếu là thông báo cá nhân, xóa các thông báo cá nhân cũ
      _notifications.removeWhere((notification) => 
        !notification.isPublic || notification.targetUserIds != null);
    }

    // Thêm thông báo mới
    _notifications.addAll(newNotifications);
    
    // Sắp xếp theo thời gian
    _notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    // Cập nhật số lượng thông báo chưa đọc
    _updateUnreadCount();
    
    _isLoading.value = false;
  }

  // Cập nhật số lượng thông báo chưa đọc
  void _updateUnreadCount() {
    // Tính toán số lượng thông báo chưa đọc
    // Lưu ý: Trong triển khai thực tế, bạn cần lưu trạng thái đã đọc trong Realtime Database
    _unreadCount.value = _notifications.length;
  }

  // Xử lý lỗi
  void _handleError(dynamic error) {
    print('Error in notification stream: $error');
    _errorMessage.value = 'Lỗi khi tải thông báo: $error';
    _isLoading.value = false;
  }

  // Đánh dấu thông báo đã đọc
  Future<bool> markAsRead(String notificationId) async {
    try {
      // Trong triển khai thực tế, bạn cần gọi API để đánh dấu đã đọc
      // và cập nhật trạng thái trong Realtime Database
      return true;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu đã đọc: $e';
      return false;
    }
  }

  // Đánh dấu tất cả thông báo đã đọc
  Future<bool> markAllAsRead() async {
    try {
      // Trong triển khai thực tế, bạn cần gọi API để đánh dấu tất cả đã đọc
      // và cập nhật trạng thái trong Realtime Database
      _unreadCount.value = 0;
      return true;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi đánh dấu tất cả đã đọc: $e';
      return false;
    }
  }

  // Xóa thông báo
  Future<bool> deleteNotification(String notificationId) async {
    try {
      // Trong triển khai thực tế, bạn cần gọi API để xóa thông báo
      // và cập nhật trạng thái trong Realtime Database
      _notifications.removeWhere((notification) => notification.id == notificationId);
      return true;
    } catch (e) {
      _errorMessage.value = 'Lỗi khi xóa thông báo: $e';
      return false;
    }
  }

  // Làm mới thông báo
  void refreshNotifications() {
    _cancelSubscriptions();
    _notifications.clear();
    _fetchNotifications();
  }

  // Xóa lỗi
  void clearError() {
    _errorMessage.value = '';
  }
}
