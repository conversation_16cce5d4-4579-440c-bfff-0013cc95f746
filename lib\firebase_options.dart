// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAPfNg2fuYnrjkSXIQIimC7WPbbhl1tRM8',
    appId: '1:359841585752:web:8deb63e00e446dcbe7c82d',
    messagingSenderId: '359841585752',
    projectId: 'moviefinder-98',
    authDomain: 'moviefinder-98.firebaseapp.com',
    storageBucket: 'moviefinder-98.firebasestorage.app',
    measurementId: 'G-BPQ9J5VZFR',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAsHeRaNQylGTQtkJEHzPkgpGbxP_jAp1Y',
    appId: '1:359841585752:android:930165b057033207e7c82d',
    messagingSenderId: '359841585752',
    projectId: 'moviefinder-98',
    storageBucket: 'moviefinder-98.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC15EJaXYcpr6a-dBQj5a1tnCuVB8ntKZ0',
    appId: '1:359841585752:ios:bddc0087e981a190e7c82d',
    messagingSenderId: '359841585752',
    projectId: 'moviefinder-98',
    storageBucket: 'moviefinder-98.firebasestorage.app',
    iosBundleId: 'com.example.movieFinder',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAPfNg2fuYnrjkSXIQIimC7WPbbhl1tRM8',
    appId: '1:359841585752:web:3a22c2faabe89b33e7c82d',
    messagingSenderId: '359841585752',
    projectId: 'moviefinder-98',
    authDomain: 'moviefinder-98.firebaseapp.com',
    storageBucket: 'moviefinder-98.firebasestorage.app',
    measurementId: 'G-J7N4ZC8R2F',
  );
}
