# Test Trailer Functionality

## Chức năng đã thêm:

### 1. TrailerPlayer Widget
- **Vị trí**: `lib/widgets/trailer_player.dart`
- **Chức năng**:
  - Hỗ trợ phát video từ URL trực tiếp
  - Hỗ trợ phát video YouTube
  - <PERSON><PERSON> controls play/pause cho video thường
  - Tự động detect YouTube URLs
  - Error handling khi không thể tải video

### 2. Tích hợp vào MovieDetailsPage
- **Vị trí**: `lib/view/page/movie_detail_page.dart`
- **Thay đổi**:
  - Thêm import TrailerPlayer widget
  - Thêm TrailerPlayer vào phần nội dung phim (sau overview, trước cast)
  - **CHỈ hiển thị khi movie có trailerUrl từ database**
  - **ĐÃ XÓA trailer mẫu khỏi legacy movie**

### 3. Admin Form đã sẵn sàng
- **Vị trí**: `lib/view/admin/movie_edit_page.dart`
- **Trường**: "Trailer URL" với hint "https://youtube.com/watch?v=..."
- Ad<PERSON> có thể thêm/chỉnh sửa trailerUrl khi tạo hoặc cập nhật phim

### 4. Dependencies đã thêm
- **video_player**: ^2.8.2 - Để phát video thường
- **youtube_player_flutter**: ^8.1.2 - Để phát video YouTube

## Cách test:

### Test với phim từ database:
1. **Admin thêm trailer URL**:
   - Vào Admin Panel → Quản lý phim
   - Tạo phim mới hoặc chỉnh sửa phim có sẵn
   - Nhập Trailer URL (YouTube hoặc video trực tiếp)
   - Lưu phim

2. **User xem trailer**:
   - Vào trang chi tiết phim có trailerUrl
   - Scroll xuống phần trailer (sau mô tả, trước cast)
   - Nhấn play để xem trailer

### Test với legacy movie:
- **KHÔNG CÒN TRAILER** - Legacy movie Thor không có trailer nữa
- Chỉ hiển thị trailer cho phim có trailerUrl từ database

## Trailer URLs để test:
- **YouTube**: https://www.youtube.com/watch?v=dQw4w9WgXcQ
- **YouTube**: https://www.youtube.com/watch?v=npvJ9FTgZbM (Thor: The Dark World)
- **Direct video**: URL video .mp4 trực tiếp

## Lưu ý:
- ✅ Trailer CHỈ hiển thị khi phim có trailerUrl từ database
- ✅ YouTube videos sử dụng YouTube player
- ✅ Video thường sử dụng video player với controls
- ✅ Error handling khi không thể tải video
- ✅ UI responsive và phù hợp với theme của app
- ✅ Admin có thể dễ dàng thêm/chỉnh sửa trailer URL

## ✅ **CẬP NHẬT MỚI: Smart Booking Button**

### **Logic nút đặt vé thông minh:**

#### **1. Kiểm tra trạng thái phim:**
- **Đang chiếu (nowPlaying)** + **Có lịch chiếu** → **"Đặt vé ngay"** (màu vàng, có thể click)
- **Đang chiếu (nowPlaying)** + **Không có lịch chiếu** → **"Chưa có lịch chiếu"** (màu cam, không click được)
- **Sắp chiếu (upcoming)** → **"Sắp ra mắt"** (màu xanh, không click được)
- **Đã kết thúc (ended)** → **"Phim đã kết thúc"** (màu xám, không click được)

#### **2. Tính năng:**
- ✅ **Tự động kiểm tra lịch chiếu** khi load trang chi tiết phim
- ✅ **Loading state** khi đang kiểm tra lịch chiếu
- ✅ **Icon phù hợp** cho từng trạng thái
- ✅ **Màu sắc trực quan** để người dùng hiểu ngay trạng thái
- ✅ **Chỉ cho phép đặt vé** khi phim đang chiếu VÀ có lịch chiếu khả dụng

#### **3. Cách hoạt động:**
1. Khi vào trang chi tiết phim → Tự động gọi `_checkMovieShowtimes()`
2. Kiểm tra `ShowtimeService.getShowtimesByMovie()` để lấy lịch chiếu
3. Lọc ra các lịch chiếu `isBookable` (còn vé, chưa hủy, chưa kết thúc)
4. Hiển thị nút phù hợp dựa trên `movie.status` và `hasAvailableShowtimes`

#### **4. Files đã cập nhật:**
- `lib/view/page/movie_detail_page.dart` - Thêm logic kiểm tra và UI nút thông minh

---

## 🔧 **KHẮC PHỤC LỖI CAMERA TRÊN THIẾT BỊ THẬT**

### **Lỗi gặp phải:**
```
I/OplusCameraManagerGlobal(26746): setClientInfo, packageName: com.example.movie_finder
W/libc    (26746): Access denied finding property "vendor.camera.aux.packagelist"
```

### **Nguyên nhân:**
- Lỗi quyền camera trên thiết bị Oppo/OnePlus
- Ứng dụng chưa được cấp quyền runtime

### **Giải pháp đã triển khai:**

#### **1. Cập nhật AndroidManifest.xml:**
- ✅ Thêm `READ_MEDIA_IMAGES` permission cho Android 13+
- ✅ Thêm `maxSdkVersion="28"` cho WRITE_EXTERNAL_STORAGE
- ✅ Thêm camera features với `required="false"`

#### **2. Thêm PermissionService:**
- **File**: `lib/services/permission_service.dart`
- **Chức năng**:
  - ✅ Kiểm tra và yêu cầu quyền camera
  - ✅ Kiểm tra và yêu cầu quyền storage/photos
  - ✅ Tự động detect Android version
  - ✅ Hiển thị dialog hướng dẫn mở Settings
  - ✅ Error handling và user-friendly messages

#### **3. Cập nhật ProfileEditPage:**
- **File**: `lib/view/page/profile_edit_page.dart`
- **Cải tiến**:
  - ✅ Tích hợp PermissionService
  - ✅ Kiểm tra quyền trước khi mở image picker
  - ✅ Dialog chọn nguồn ảnh (Camera/Gallery)
  - ✅ Thông báo rõ ràng cho user

#### **4. Dependencies mới:**
- `permission_handler: ^11.3.1` - Quản lý quyền runtime

### **Cách khắc phục thủ công:**

#### **Trên thiết bị:**
1. **Settings** → **Apps** → **Đớp Phim**
2. **Permissions** → Bật **Camera** và **Storage**

#### **Hoặc khi sử dụng app:**
1. Vào **Profile** → **Edit Profile**
2. Nhấn vào ảnh đại diện
3. Cho phép quyền khi popup xuất hiện

### **Test sau khi sửa:**
1. ✅ Build thành công
2. ✅ Không còn lỗi analyze
3. ✅ Permission handling hoạt động
4. ✅ User experience được cải thiện
