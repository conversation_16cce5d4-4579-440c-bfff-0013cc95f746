import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/movie_controller.dart';

class SharePage extends StatelessWidget {
  const SharePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final movieController = Get.find<MovieController>();

    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'share_movies'.tr,
              style: GoogleFonts.mulish(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 20),

            // Search bar
            TextField(
              onChanged: (value) {
                if (value.length > 2) {
                  movieController.searchMovies(value);
                } else if (value.isEmpty) {
                  movieController.clearSearch();
                }
              },
              style: GoogleFonts.mulish(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'search_to_share'.tr,
                hintStyle: GoogleFonts.mulish(color: Colors.white60),
                prefixIcon: const Icon(Icons.search, color: Colors.white60),
                filled: true,
                fillColor: Colors.white.withOpacity(0.1),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),

            const SizedBox(height: 20),

            // Search results or popular movies
            Expanded(
              child: Obx(() {
                if (movieController.isLoadingSearch.value ||
                    movieController.isLoadingPopular.value) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                final movies = movieController.searchQuery.value.isNotEmpty
                    ? movieController.searchResults
                    : movieController.popularMovies;

                if (movies.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.movie_outlined,
                          size: 80,
                          color: Colors.white.withOpacity(0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          movieController.searchQuery.value.isNotEmpty
                              ? 'no_movies_found'.tr
                              : 'no_popular_movies'.tr,
                          style: GoogleFonts.mulish(
                            fontSize: 18,
                            color: Colors.white.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: movies.length,
                  itemBuilder: (context, index) {
                    final movie = movies[index];
                    return _buildShareCard(context, movie);
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShareCard(BuildContext context, dynamic movie) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.white.withOpacity(0.1),
      ),
      child: Row(
        children: [
          // Movie poster
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              bottomLeft: Radius.circular(12),
            ),
            child: Image.network(
              movie.fullPosterPath,
              height: 120,
              width: 80,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 120,
                  width: 80,
                  color: Colors.grey[800],
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white54,
                    ),
                  ),
                );
              },
            ),
          ),
          // Movie details
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    movie.title,
                    style: GoogleFonts.mulish(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (movie.releaseDate != null &&
                      movie.releaseDate!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Text(
                        movie.year,
                        style: GoogleFonts.mulish(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                    ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        movie.rating,
                        style: GoogleFonts.mulish(
                          fontSize: 14,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // Share button
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    _showShareOptions(context, movie);
                  },
                  icon: const Icon(
                    Icons.share,
                    color: Colors.white,
                  ),
                  style: IconButton.styleFrom(
                    backgroundColor: const Color(0xff2B5876),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'share'.tr,
                  style: GoogleFonts.mulish(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showShareOptions(BuildContext context, dynamic movie) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xff2B5876),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Share "${movie.title}"',
                style: GoogleFonts.mulish(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildShareOption(
                    icon: Icons.message,
                    label: 'message'.tr,
                    onTap: () {
                      Navigator.pop(context);
                      Get.snackbar(
                        'Share via Message',
                        'This feature is coming soon',
                        snackPosition: SnackPosition.BOTTOM,
                      );
                    },
                  ),
                  _buildShareOption(
                    icon: Icons.email,
                    label: 'email_share'.tr,
                    onTap: () {
                      Navigator.pop(context);
                      Get.snackbar(
                        'Share via Email',
                        'This feature is coming soon',
                        snackPosition: SnackPosition.BOTTOM,
                      );
                    },
                  ),
                  _buildShareOption(
                    icon: Icons.facebook,
                    label: 'facebook'.tr,
                    onTap: () {
                      Navigator.pop(context);
                      Get.snackbar(
                        'Share to Facebook',
                        'This feature is coming soon',
                        snackPosition: SnackPosition.BOTTOM,
                      );
                    },
                  ),
                  _buildShareOption(
                    icon: Icons.link,
                    label: 'copy_link'.tr,
                    onTap: () {
                      Navigator.pop(context);
                      Get.snackbar(
                        'link_copied'.tr,
                        'movie_link_copied'.tr,
                        snackPosition: SnackPosition.BOTTOM,
                      );
                    },
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  Widget _buildShareOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.mulish(
              fontSize: 12,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
