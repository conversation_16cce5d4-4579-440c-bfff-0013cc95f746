enum ChatType { userSupport, bugReport }

extension ChatTypeExtension on ChatType {
  String get name {
    switch (this) {
      case ChatType.userSupport:
        return 'user_support';
      case ChatType.bugReport:
        return 'bug_report';
    }
  }

  static ChatType fromString(String? value) {
    switch (value) {
      case 'bug_report':
        return ChatType.bugReport;
      default:
        return ChatType.userSupport;
    }
  }

  String get displayName {
    switch (this) {
      case ChatType.userSupport:
        return 'Hỗ Trợ Người Dùng';
      case ChatType.bugReport:
        return 'Báo Cáo Lỗi';
    }
  }
}

enum ChatStatus { active, closed }

extension ChatStatusExtension on ChatStatus {
  String get name {
    switch (this) {
      case ChatStatus.active:
        return 'active';
      case ChatStatus.closed:
        return 'closed';
    }
  }

  static ChatStatus fromString(String? value) {
    switch (value) {
      case 'closed':
        return ChatStatus.closed;
      default:
        return ChatStatus.active;
    }
  }

  String get displayName {
    switch (this) {
      case ChatStatus.active:
        return 'Đang <PERSON>';
      case ChatStatus.closed:
        return 'Đã Đóng';
    }
  }
}

enum MessageType { text, image, file }

extension MessageTypeExtension on MessageType {
  String get name {
    switch (this) {
      case MessageType.text:
        return 'text';
      case MessageType.image:
        return 'image';
      case MessageType.file:
        return 'file';
    }
  }

  static MessageType fromString(String? value) {
    switch (value) {
      case 'image':
        return MessageType.image;
      case 'file':
        return MessageType.file;
      default:
        return MessageType.text;
    }
  }

  String get displayName {
    switch (this) {
      case MessageType.text:
        return 'Văn Bản';
      case MessageType.image:
        return 'Hình Ảnh';
      case MessageType.file:
        return 'Tệp Tin';
    }
  }
}

class ChatMessageModel {
  final String id;
  final String senderId;
  final String senderName;
  final String message;
  final MessageType type;
  final int timestamp;
  final bool isRead;

  ChatMessageModel({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
  });

  factory ChatMessageModel.fromJson(String id, Map<String, dynamic> json) {
    return ChatMessageModel(
      id: id,
      senderId: json['senderId'] ?? '',
      senderName: json['senderName'] ?? '',
      message: json['message'] ?? '',
      type: MessageTypeExtension.fromString(json['type']),
      timestamp: json['timestamp'] ?? 0,
      isRead: json['isRead'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'senderId': senderId,
      'senderName': senderName,
      'message': message,
      'type': type.name,
      'timestamp': timestamp,
      'isRead': isRead,
    };
  }

  ChatMessageModel copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? message,
    MessageType? type,
    int? timestamp,
    bool? isRead,
  }) {
    return ChatMessageModel(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
    );
  }

  // Helper methods
  DateTime get dateTime => DateTime.fromMillisecondsSinceEpoch(timestamp);
  bool get isText => type == MessageType.text;
  bool get isImage => type == MessageType.image;
  bool get isFile => type == MessageType.file;

  String get timeAgo {
    final now = DateTime.now();
    final messageTime = dateTime;
    final difference = now.difference(messageTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} ngày trước';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} giờ trước';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} phút trước';
    } else {
      return 'Vừa xong';
    }
  }

  String get formattedTime {
    final messageTime = dateTime;
    return '${messageTime.hour.toString().padLeft(2, '0')}:${messageTime.minute.toString().padLeft(2, '0')}';
  }
}

class ChatSupportModel {
  final String id;
  final List<String> participants; // userIds
  final ChatType type;
  final ChatStatus status;
  final int createdAt;
  final int updatedAt;
  final Map<String, ChatMessageModel> messages;

  ChatSupportModel({
    required this.id,
    required this.participants,
    required this.type,
    this.status = ChatStatus.active,
    required this.createdAt,
    required this.updatedAt,
    this.messages = const {},
  });

  factory ChatSupportModel.fromJson(String id, Map<String, dynamic> json) {
    Map<String, ChatMessageModel> messagesMap = {};
    if (json['messages'] != null) {
      final messagesData = json['messages'] as Map<String, dynamic>;
      messagesData.forEach((messageId, messageData) {
        messagesMap[messageId] = ChatMessageModel.fromJson(
          messageId,
          Map<String, dynamic>.from(messageData as Map),
        );
      });
    }

    return ChatSupportModel(
      id: id,
      participants: List<String>.from(json['participants'] ?? []),
      type: ChatTypeExtension.fromString(json['type']),
      status: ChatStatusExtension.fromString(json['status']),
      createdAt: json['createdAt'] ?? 0,
      updatedAt: json['updatedAt'] ?? 0,
      messages: messagesMap,
    );
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> messagesMap = {};
    messages.forEach((messageId, message) {
      messagesMap[messageId] = message.toJson();
    });

    return {
      'participants': participants,
      'type': type.name,
      'status': status.name,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'messages': messagesMap,
    };
  }

  ChatSupportModel copyWith({
    String? id,
    List<String>? participants,
    ChatType? type,
    ChatStatus? status,
    int? createdAt,
    int? updatedAt,
    Map<String, ChatMessageModel>? messages,
  }) {
    return ChatSupportModel(
      id: id ?? this.id,
      participants: participants ?? this.participants,
      type: type ?? this.type,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      messages: messages ?? this.messages,
    );
  }

  // Helper methods
  bool get isActive => status == ChatStatus.active;
  bool get isClosed => status == ChatStatus.closed;
  bool get isUserSupport => type == ChatType.userSupport;
  bool get isBugReport => type == ChatType.bugReport;

  DateTime get createdDateTime => DateTime.fromMillisecondsSinceEpoch(createdAt);
  DateTime get updatedDateTime => DateTime.fromMillisecondsSinceEpoch(updatedAt);

  List<ChatMessageModel> get messagesList {
    final list = messages.values.toList();
    list.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return list;
  }

  List<ChatMessageModel> get unreadMessages {
    return messagesList.where((message) => !message.isRead).toList();
  }

  int get unreadCount => unreadMessages.length;
  bool get hasUnreadMessages => unreadCount > 0;

  ChatMessageModel? get lastMessage {
    if (messagesList.isEmpty) return null;
    return messagesList.last;
  }

  String get displayTitle {
    switch (type) {
      case ChatType.userSupport:
        return 'Hỗ Trợ Khách Hàng';
      case ChatType.bugReport:
        return 'Báo Cáo Lỗi';
    }
  }

  String get displayStatus => status.displayName;

  bool isParticipant(String userId) => participants.contains(userId);

  int getUnreadCountForUser(String userId) {
    return messagesList
        .where((message) => message.senderId != userId && !message.isRead)
        .length;
  }

  List<ChatMessageModel> getMessagesFromUser(String userId) {
    return messagesList.where((message) => message.senderId == userId).toList();
  }

  List<ChatMessageModel> getMessagesToUser(String userId) {
    return messagesList.where((message) => message.senderId != userId).toList();
  }
}
