import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/theater_model.dart';
import '../../services/theater_service.dart';
import 'theater_detail_page.dart';
import 'add_theater_page.dart';
import 'import_data_page.dart';

class TheaterManagementPage extends StatefulWidget {
  const TheaterManagementPage({Key? key}) : super(key: key);

  @override
  State<TheaterManagementPage> createState() => _TheaterManagementPageState();
}

class _TheaterManagementPageState extends State<TheaterManagementPage> {
  final TheaterService _theaterService = TheaterService();
  final RxList<TheaterModel> _theaters = <TheaterModel>[].obs;
  final RxList<TheaterModel> _filteredTheaters = <TheaterModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _showActiveOnly = true.obs;
  final TextEditingController _searchController = TextEditingController();
  final RxString _selectedCity = ''.obs;
  final RxList<String> _cities = <String>[].obs;

  @override
  void initState() {
    super.initState();
    _loadTheaters();
    _loadCities();
  }

  Future<void> _loadTheaters() async {
    try {
      _isLoading.value = true;
      final theaters = await _theaterService.getAllTheaters(
          activeOnly: _showActiveOnly.value);
      _theaters.value = theaters;
      // Apply current filters after loading
      _filterTheaters();
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể tải danh sách rạp: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _loadCities() async {
    try {
      // Admin can see cities from all theaters (including inactive)
      final cities =
          await _theaterService.getAvailableCities(activeOnly: false);
      _cities.value = cities;
    } catch (e) {
      print('Error loading cities: $e');
    }
  }

  void _filterTheaters() {
    final query = _searchController.text.toLowerCase();
    final selectedCity = _selectedCity.value;

    _filteredTheaters.value = _theaters.where((theater) {
      final matchesSearch = theater.name.toLowerCase().contains(query) ||
          theater.address.fullAddress.toLowerCase().contains(query);
      final matchesCity =
          selectedCity.isEmpty || theater.address.city == selectedCity;

      return matchesSearch && matchesCity;
    }).toList();
  }

  void _toggleShowActiveOnly() {
    _showActiveOnly.value = !_showActiveOnly.value;
    _loadTheaters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Quản Lý Rạp Chiếu',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _showImportMenu(),
                      icon: const Icon(Icons.upload_file, color: Colors.white),
                    ),
                    IconButton(
                      onPressed: () => Get.to(() => const AddTheaterPage())
                          ?.then((_) => _loadTheaters()),
                      icon: const Icon(Icons.add, color: Colors.white),
                    ),
                  ],
                ),
              ),

              // Search and Filter
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    // Search Bar
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: TextField(
                        controller: _searchController,
                        onChanged: (_) => _filterTheaters(),
                        style: const TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          hintText: 'Tìm kiếm rạp...',
                          hintStyle:
                              TextStyle(color: Colors.white.withOpacity(0.7)),
                          prefixIcon: Icon(Icons.search,
                              color: Colors.white.withOpacity(0.7)),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(16),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),

                    // City Filter
                    Obx(() => Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String>(
                              value: _selectedCity.value.isEmpty
                                  ? null
                                  : _selectedCity.value,
                              hint: Text(
                                'Chọn thành phố',
                                style: TextStyle(
                                    color: Colors.white.withOpacity(0.7)),
                              ),
                              dropdownColor: const Color(0xff2B5876),
                              style: const TextStyle(color: Colors.white),
                              items: [
                                DropdownMenuItem<String>(
                                  value: '',
                                  child: Text(
                                    'Tất cả thành phố',
                                    style:
                                        GoogleFonts.mulish(color: Colors.white),
                                  ),
                                ),
                                ..._cities
                                    .map((city) => DropdownMenuItem<String>(
                                          value: city,
                                          child: Text(
                                            city,
                                            style: GoogleFonts.mulish(
                                                color: Colors.white),
                                          ),
                                        )),
                              ],
                              onChanged: (value) {
                                _selectedCity.value = value ?? '';
                                _filterTheaters();
                              },
                            ),
                          ),
                        )),
                    const SizedBox(height: 12),

                    // Filter Controls Row
                    Row(
                      children: [
                        // Show All Toggle
                        Expanded(
                          child: Obx(() => GestureDetector(
                                onTap: _toggleShowActiveOnly,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 12),
                                  decoration: BoxDecoration(
                                    color: _showActiveOnly.value
                                        ? Colors.white.withOpacity(0.1)
                                        : Colors.blue.withOpacity(0.3),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: _showActiveOnly.value
                                          ? Colors.white.withOpacity(0.3)
                                          : Colors.blue,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        _showActiveOnly.value
                                            ? Icons.visibility_off
                                            : Icons.visibility,
                                        color: Colors.white,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        _showActiveOnly.value
                                            ? 'Chỉ hoạt động'
                                            : 'Hiển thị tất cả',
                                        style: GoogleFonts.mulish(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )),
                        ),
                        const SizedBox(width: 12),

                        // Reload Button
                        GestureDetector(
                          onTap: _loadTheaters,
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                  color: Colors.white.withOpacity(0.3)),
                            ),
                            child: const Icon(
                              Icons.refresh,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Theater List
              Expanded(
                child: Obx(() {
                  if (_isLoading.value) {
                    return const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    );
                  }

                  if (_filteredTheaters.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.movie_outlined,
                            size: 64,
                            color: Colors.white.withOpacity(0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Không có rạp nào',
                            style: GoogleFonts.mulish(
                              fontSize: 18,
                              color: Colors.white.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: _loadTheaters,
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _filteredTheaters.length,
                      itemBuilder: (context, index) {
                        final theater = _filteredTheaters[index];
                        return _buildTheaterCard(theater);
                      },
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTheaterCard(TheaterModel theater) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0x1EA6A1E0), Color(0x1EA1F3FE)],
            ),
            borderRadius: BorderRadius.circular(25),
          ),
          child: const Icon(
            Icons.movie,
            color: Colors.white,
            size: 24,
          ),
        ),
        title: Text(
          theater.name,
          style: GoogleFonts.mulish(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              theater.address.fullAddress,
              style: GoogleFonts.mulish(
                fontSize: 12,
                color: Colors.white.withOpacity(0.7),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              children: theater.facilities
                  .take(3)
                  .map(
                    (facility) => Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.withOpacity(0.5)),
                      ),
                      child: Text(
                        facility,
                        style: GoogleFonts.mulish(
                          fontSize: 10,
                          color: Colors.blue[200],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: theater.isActive
                    ? Colors.green.withOpacity(0.2)
                    : Colors.red.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theater.isActive ? Colors.green : Colors.red,
                ),
              ),
              child: Text(
                theater.isActive ? 'Hoạt động' : 'Tạm dừng',
                style: GoogleFonts.mulish(
                  fontSize: 10,
                  color: theater.isActive ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            const Icon(Icons.arrow_forward_ios, color: Colors.white, size: 16),
          ],
        ),
        onTap: () => Get.to(() => TheaterDetailPage(theater: theater))
            ?.then((_) => _loadTheaters()),
      ),
    );
  }

  void _showImportMenu() {
    Get.to(() => const ImportDataPage(dataType: ImportDataType.theater))
        ?.then((_) {
      // Refresh theaters list after import
      _loadTheaters();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
