import 'package:cloud_firestore/cloud_firestore.dart';

/// <PERSON><PERSON>ình cho thông báo chung
class NotificationModel {
  final String id;
  final String title;
  final String body;
  final String? imageUrl;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final String? targetScreen;
  final Map<String, dynamic>?
      data; // Thay đổi từ Map<String, String>? sang Map<String, dynamic>?
  final bool isPublic; // Thông báo công khai cho tất cả người dùng
  final List<String>?
      targetUserIds; // Danh sách người dùng nhận thông báo (nếu không phải public)

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    this.imageUrl,
    required this.createdAt,
    this.expiresAt,
    this.targetScreen,
    this.data,
    this.isPublic = true,
    this.targetUserIds,
  });

  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NotificationModel(
      id: doc.id,
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      imageUrl: data['imageUrl'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      expiresAt: data['expiresAt'] != null
          ? (data['expiresAt'] as Timestamp).toDate()
          : null,
      targetScreen: data['targetScreen'],
      data: data['data'],
      isPublic: data['isPublic'] ?? true,
      targetUserIds: data['targetUserIds'] != null
          ? List<String>.from(data['targetUserIds'])
          : null,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'body': body,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'expiresAt': expiresAt != null ? Timestamp.fromDate(expiresAt!) : null,
      'targetScreen': targetScreen,
      'data': data,
      'isPublic': isPublic,
      'targetUserIds': targetUserIds,
    };
  }
}

/// Mô hình cho trạng thái thông báo của người dùng
class UserNotificationModel {
  final String id;
  final String userId;
  final String notificationId;
  final bool isRead;
  final bool isSeen;
  final bool isDeleted;
  final DateTime createdAt;
  final DateTime? readAt;
  final DateTime? seenAt;
  final DateTime? deletedAt;

  UserNotificationModel({
    required this.id,
    required this.userId,
    required this.notificationId,
    this.isRead = false,
    this.isSeen = false,
    this.isDeleted = false,
    required this.createdAt,
    this.readAt,
    this.seenAt,
    this.deletedAt,
  });

  factory UserNotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserNotificationModel(
      id: doc.id,
      userId: data['userId'] ?? '',
      notificationId: data['notificationId'] ?? '',
      isRead: data['isRead'] ?? false,
      isSeen: data['isSeen'] ?? false,
      isDeleted: data['isDeleted'] ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      readAt: data['readAt'] != null
          ? (data['readAt'] as Timestamp).toDate()
          : null,
      seenAt: data['seenAt'] != null
          ? (data['seenAt'] as Timestamp).toDate()
          : null,
      deletedAt: data['deletedAt'] != null
          ? (data['deletedAt'] as Timestamp).toDate()
          : null,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'userId': userId,
      'notificationId': notificationId,
      'isRead': isRead,
      'isSeen': isSeen,
      'isDeleted': isDeleted,
      'createdAt': Timestamp.fromDate(createdAt),
      'readAt': readAt != null ? Timestamp.fromDate(readAt!) : null,
      'seenAt': seenAt != null ? Timestamp.fromDate(seenAt!) : null,
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
    };
  }

  UserNotificationModel copyWith({
    String? id,
    String? userId,
    String? notificationId,
    bool? isRead,
    bool? isSeen,
    bool? isDeleted,
    DateTime? createdAt,
    DateTime? readAt,
    DateTime? seenAt,
    DateTime? deletedAt,
  }) {
    return UserNotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      notificationId: notificationId ?? this.notificationId,
      isRead: isRead ?? this.isRead,
      isSeen: isSeen ?? this.isSeen,
      isDeleted: isDeleted ?? this.isDeleted,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      seenAt: seenAt ?? this.seenAt,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }
}

/// Mô hình kết hợp thông báo và trạng thái của người dùng
class UserNotificationViewModel {
  final NotificationModel notification;
  final UserNotificationModel userNotification;

  UserNotificationViewModel({
    required this.notification,
    required this.userNotification,
  });
}
