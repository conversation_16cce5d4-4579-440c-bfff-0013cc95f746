import 'package:get/get.dart';
import '../controllers/realtime_notification_controller.dart';
import '../controllers/realtime_bug_report_controller.dart';

class RealtimeDatabaseBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<RealtimeNotificationController>(
      () => RealtimeNotificationController(),
      fenix: true,
    );
    
    Get.lazyPut<RealtimeBugReportController>(
      () => RealtimeBugReportController(),
      fenix: true,
    );
  }
}
