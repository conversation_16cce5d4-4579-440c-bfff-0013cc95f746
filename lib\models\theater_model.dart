import 'package:cloud_firestore/cloud_firestore.dart';

class TheaterCoordinates {
  final double latitude;
  final double longitude;

  TheaterCoordinates({
    required this.latitude,
    required this.longitude,
  });

  factory TheaterCoordinates.fromJson(Map<String, dynamic> json) {
    return TheaterCoordinates(
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  TheaterCoordinates copyWith({
    double? latitude,
    double? longitude,
  }) {
    return TheaterCoordinates(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }
}

class TheaterAddress {
  final String street;
  final String district;
  final String city;
  final String province;
  final TheaterCoordinates coordinates;

  TheaterAddress({
    required this.street,
    required this.district,
    required this.city,
    required this.province,
    required this.coordinates,
  });

  factory TheaterAddress.fromJson(Map<String, dynamic> json) {
    return TheaterAddress(
      street: json['street'] ?? '',
      district: json['district'] ?? '',
      city: json['city'] ?? '',
      province: json['province'] ?? '',
      coordinates: TheaterCoordinates.from<PERSON>son(json['coordinates'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'district': district,
      'city': city,
      'province': province,
      'coordinates': coordinates.toJson(),
    };
  }

  String get fullAddress => '$street, $district, $city, $province';

  TheaterAddress copyWith({
    String? street,
    String? district,
    String? city,
    String? province,
    TheaterCoordinates? coordinates,
  }) {
    return TheaterAddress(
      street: street ?? this.street,
      district: district ?? this.district,
      city: city ?? this.city,
      province: province ?? this.province,
      coordinates: coordinates ?? this.coordinates,
    );
  }
}

class TheaterOperatingHours {
  final String open;
  final String close;

  TheaterOperatingHours({
    required this.open,
    required this.close,
  });

  factory TheaterOperatingHours.fromJson(Map<String, dynamic> json) {
    return TheaterOperatingHours(
      open: json['open'] ?? '08:00',
      close: json['close'] ?? '23:00',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'open': open,
      'close': close,
    };
  }

  TheaterOperatingHours copyWith({
    String? open,
    String? close,
  }) {
    return TheaterOperatingHours(
      open: open ?? this.open,
      close: close ?? this.close,
    );
  }
}

class TheaterModel {
  final String id;
  final String name;
  final TheaterAddress address;
  final String phoneNumber;
  final String? email;
  final List<String> facilities; // "parking", "food_court", "3d", "imax", "vip"
  final Map<String, TheaterOperatingHours> operatingHours;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  TheaterModel({
    required this.id,
    required this.name,
    required this.address,
    required this.phoneNumber,
    this.email,
    this.facilities = const [],
    required this.operatingHours,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TheaterModel.fromJson(Map<String, dynamic> json) {
    Map<String, TheaterOperatingHours> operatingHoursMap = {};
    if (json['operatingHours'] != null) {
      final hoursData = json['operatingHours'] as Map<String, dynamic>;
      hoursData.forEach((day, hours) {
        operatingHoursMap[day] = TheaterOperatingHours.fromJson(hours);
      });
    }

    return TheaterModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      address: TheaterAddress.fromJson(json['address'] ?? {}),
      phoneNumber: json['phoneNumber'] ?? '',
      email: json['email'],
      facilities: List<String>.from(json['facilities'] ?? []),
      operatingHours: operatingHoursMap,
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null 
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? (json['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  factory TheaterModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return TheaterModel.fromJson({...data, 'id': doc.id});
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> operatingHoursMap = {};
    operatingHours.forEach((day, hours) {
      operatingHoursMap[day] = hours.toJson();
    });

    return {
      'name': name,
      'address': address.toJson(),
      'phoneNumber': phoneNumber,
      'email': email,
      'facilities': facilities,
      'operatingHours': operatingHoursMap,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Map<String, dynamic> toFirestore() => toJson();

  TheaterModel copyWith({
    String? id,
    String? name,
    TheaterAddress? address,
    String? phoneNumber,
    String? email,
    List<String>? facilities,
    Map<String, TheaterOperatingHours>? operatingHours,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TheaterModel(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      facilities: facilities ?? this.facilities,
      operatingHours: operatingHours ?? this.operatingHours,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get hasParking => facilities.contains('parking');
  bool get hasFoodCourt => facilities.contains('food_court');
  bool get has3D => facilities.contains('3d');
  bool get hasIMAX => facilities.contains('imax');
  bool get hasVIP => facilities.contains('vip');

  TheaterOperatingHours? getOperatingHours(String day) {
    return operatingHours[day.toLowerCase()];
  }

  bool isOpenOn(String day) {
    final hours = getOperatingHours(day);
    return hours != null;
  }

  double distanceFrom(double latitude, double longitude) {
    // Simple distance calculation (you might want to use a more accurate formula)
    final lat1 = address.coordinates.latitude;
    final lon1 = address.coordinates.longitude;
    final lat2 = latitude;
    final lon2 = longitude;
    
    return ((lat1 - lat2).abs() + (lon1 - lon2).abs());
  }
}
