# Flutter Wrapper
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-keep class io.flutter.plugin.editing.** { *; }

# Image Picker
-keep class io.flutter.plugins.imagepicker.** { *; }
-keep class androidx.core.content.FileProvider
-keep class androidx.core.app.ActivityCompat

# Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
