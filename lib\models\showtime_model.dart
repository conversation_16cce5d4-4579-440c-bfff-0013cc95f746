import 'package:cloud_firestore/cloud_firestore.dart';

enum ShowtimeStatus { active, cancelled, full, ended }

extension ShowtimeStatusExtension on ShowtimeStatus {
  String get name {
    switch (this) {
      case ShowtimeStatus.active:
        return 'active';
      case ShowtimeStatus.cancelled:
        return 'cancelled';
      case ShowtimeStatus.full:
        return 'full';
      case ShowtimeStatus.ended:
        return 'ended';
    }
  }

  static ShowtimeStatus fromString(String? value) {
    switch (value) {
      case 'cancelled':
        return ShowtimeStatus.cancelled;
      case 'full':
        return ShowtimeStatus.full;
      case 'ended':
        return ShowtimeStatus.ended;
      default:
        return ShowtimeStatus.active;
    }
  }

  String get displayName {
    switch (this) {
      case ShowtimeStatus.active:
        return 'Đang <PERSON>án';
      case ShowtimeStatus.cancelled:
        return 'Đã Hủy';
      case ShowtimeStatus.full:
        return 'Hết Vé';
      case ShowtimeStatus.ended:
        return 'Đã Kết Thúc';
    }
  }
}

class ShowtimePricing {
  final double standard;
  final double vip;
  final double couple;
  final double student;
  final double senior;

  ShowtimePricing({
    required this.standard,
    required this.vip,
    required this.couple,
    required this.student,
    required this.senior,
  });

  factory ShowtimePricing.fromJson(Map<String, dynamic> json) {
    return ShowtimePricing(
      standard: json['standard']?.toDouble() ?? 0.0,
      vip: json['vip']?.toDouble() ?? 0.0,
      couple: json['couple']?.toDouble() ?? 0.0,
      student: json['student']?.toDouble() ?? 0.0,
      senior: json['senior']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'standard': standard,
      'vip': vip,
      'couple': couple,
      'student': student,
      'senior': senior,
    };
  }

  ShowtimePricing copyWith({
    double? standard,
    double? vip,
    double? couple,
    double? student,
    double? senior,
  }) {
    return ShowtimePricing(
      standard: standard ?? this.standard,
      vip: vip ?? this.vip,
      couple: couple ?? this.couple,
      student: student ?? this.student,
      senior: senior ?? this.senior,
    );
  }

  double getPriceForSeatType(String seatType) {
    switch (seatType.toLowerCase()) {
      case 'vip':
        return vip;
      case 'couple':
        return couple;
      case 'student':
        return student;
      case 'senior':
        return senior;
      default:
        return standard;
    }
  }
}

class SpecialOffer {
  final String type; // "early_bird", "group_discount", "student_discount"
  final double discount; // percentage
  final String conditions;

  SpecialOffer({
    required this.type,
    required this.discount,
    required this.conditions,
  });

  factory SpecialOffer.fromJson(Map<String, dynamic> json) {
    return SpecialOffer(
      type: json['type'] ?? '',
      discount: json['discount']?.toDouble() ?? 0.0,
      conditions: json['conditions'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'discount': discount,
      'conditions': conditions,
    };
  }

  SpecialOffer copyWith({
    String? type,
    double? discount,
    String? conditions,
  }) {
    return SpecialOffer(
      type: type ?? this.type,
      discount: discount ?? this.discount,
      conditions: conditions ?? this.conditions,
    );
  }

  String get displayName {
    switch (type) {
      case 'early_bird':
        return 'Giảm Giá Sớm';
      case 'group_discount':
        return 'Giảm Giá Nhóm';
      case 'student_discount':
        return 'Giảm Giá Sinh Viên';
      default:
        return 'Ưu Đãi Đặc Biệt';
    }
  }
}

class ShowtimeModel {
  final String id;
  final int movieId;
  final String theaterId;
  final String screenId;
  final String date; // "YYYY-MM-DD"
  final String time; // "HH:mm"
  final String endTime; // "HH:mm"
  final ShowtimePricing pricing;
  final int availableSeats;
  final List<String> bookedSeats; // ["A1", "A2", "B5"]
  final List<String> reservedSeats; // ghế đang được giữ tạm thời
  final ShowtimeStatus status;
  final List<SpecialOffer> specialOffers;
  final DateTime createdAt;
  final DateTime updatedAt;

  ShowtimeModel({
    required this.id,
    required this.movieId,
    required this.theaterId,
    required this.screenId,
    required this.date,
    required this.time,
    required this.endTime,
    required this.pricing,
    required this.availableSeats,
    this.bookedSeats = const [],
    this.reservedSeats = const [],
    this.status = ShowtimeStatus.active,
    this.specialOffers = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory ShowtimeModel.fromJson(Map<String, dynamic> json) {
    return ShowtimeModel(
      id: json['id'] ?? '',
      movieId: json['movieId'] ?? 0,
      theaterId: json['theaterId'] ?? '',
      screenId: json['screenId'] ?? '',
      date: json['date'] ?? '',
      time: json['time'] ?? '',
      endTime: json['endTime'] ?? '',
      pricing: ShowtimePricing.fromJson(json['pricing'] ?? {}),
      availableSeats: json['availableSeats'] ?? 0,
      bookedSeats: List<String>.from(json['bookedSeats'] ?? []),
      reservedSeats: List<String>.from(json['reservedSeats'] ?? []),
      status: ShowtimeStatusExtension.fromString(json['status']),
      specialOffers: (json['specialOffers'] as List<dynamic>?)
              ?.map((offer) => SpecialOffer.fromJson(offer))
              .toList() ??
          [],
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? (json['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  factory ShowtimeModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ShowtimeModel.fromJson({...data, 'id': doc.id});
  }

  Map<String, dynamic> toJson() {
    return {
      'movieId': movieId,
      'theaterId': theaterId,
      'screenId': screenId,
      'date': date,
      'time': time,
      'endTime': endTime,
      'pricing': pricing.toJson(),
      'availableSeats': availableSeats,
      'bookedSeats': bookedSeats,
      'reservedSeats': reservedSeats,
      'status': status.name,
      'specialOffers': specialOffers.map((offer) => offer.toJson()).toList(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Map<String, dynamic> toFirestore() => toJson();

  ShowtimeModel copyWith({
    String? id,
    int? movieId,
    String? theaterId,
    String? screenId,
    String? date,
    String? time,
    String? endTime,
    ShowtimePricing? pricing,
    int? availableSeats,
    List<String>? bookedSeats,
    List<String>? reservedSeats,
    ShowtimeStatus? status,
    List<SpecialOffer>? specialOffers,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ShowtimeModel(
      id: id ?? this.id,
      movieId: movieId ?? this.movieId,
      theaterId: theaterId ?? this.theaterId,
      screenId: screenId ?? this.screenId,
      date: date ?? this.date,
      time: time ?? this.time,
      endTime: endTime ?? this.endTime,
      pricing: pricing ?? this.pricing,
      availableSeats: availableSeats ?? this.availableSeats,
      bookedSeats: bookedSeats ?? this.bookedSeats,
      reservedSeats: reservedSeats ?? this.reservedSeats,
      status: status ?? this.status,
      specialOffers: specialOffers ?? this.specialOffers,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  DateTime get showDateTime {
    final dateParts = date.split('-');
    final timeParts = time.split(':');
    return DateTime(
      int.parse(dateParts[0]),
      int.parse(dateParts[1]),
      int.parse(dateParts[2]),
      int.parse(timeParts[0]),
      int.parse(timeParts[1]),
    );
  }

  DateTime get endDateTime {
    final dateParts = date.split('-');
    final timeParts = endTime.split(':');
    return DateTime(
      int.parse(dateParts[0]),
      int.parse(dateParts[1]),
      int.parse(dateParts[2]),
      int.parse(timeParts[0]),
      int.parse(timeParts[1]),
    );
  }

  bool get isBookable => status == ShowtimeStatus.active && availableSeats > 0;
  bool get isFull => availableSeats <= 0 || status == ShowtimeStatus.full;
  bool get isCancelled => status == ShowtimeStatus.cancelled;
  bool get isEnded =>
      status == ShowtimeStatus.ended || endDateTime.isBefore(DateTime.now());

  bool isSeatBooked(String seatId) => bookedSeats.contains(seatId);
  bool isSeatReserved(String seatId) => reservedSeats.contains(seatId);
  bool isSeatAvailable(String seatId) =>
      !isSeatBooked(seatId) && !isSeatReserved(seatId);

  int get totalBookedSeats => bookedSeats.length;
  int get totalReservedSeats => reservedSeats.length;
  int get totalUnavailableSeats => totalBookedSeats + totalReservedSeats;

  List<SpecialOffer> get activeOffers => specialOffers;

  double getDiscountedPrice(String seatType, {String? offerType}) {
    double basePrice = pricing.getPriceForSeatType(seatType);

    if (offerType != null) {
      final offers = specialOffers.where((o) => o.type == offerType);
      final offer = offers.isNotEmpty ? offers.first : null;
      if (offer != null) {
        return basePrice * (1 - offer.discount / 100);
      }
    }

    return basePrice;
  }

  String get displayTime => '$time - $endTime';
  String get displayDate => date;
  String get displayDateTime => '$date $time';
}
