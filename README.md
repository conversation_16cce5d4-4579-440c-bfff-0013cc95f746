# movie_finder

A new Flutter project for finding movie and its details.

# Project Screenshots
Home Page

<img src="https://raw.githubusercontent.com/ZRShamim/Movie_Finder-Flutter-/main/assets/project_progress/279225706_552121216268632_8462016636129319895_n.png" width=270 >

Movie Detail Page

<img src="https://raw.githubusercontent.com/ZRShamim/Movie_Finder-Flutter-/main/assets/project_progress/280654967_1984518965082018_8662307702767337035_n.png" width=270 >

#Project pages 
```
Home Page - Done {Designing}
Movie Detail Page - Done {Designing}
Favorite Page - Ongoing
Account Page - Ongoing
.
.
.
.
More will be added in future
```

# Design Idea
https://www.figma.com/file/znQc5wCXeO2cbCVBEn32gZ/Movie-Finder-App-Figma-Design?node-id=18%3A1779

## Youtube channels or Udemy courses or others that help me to grow
```
https://www.youtube.com/c/<PERSON>ilke
https://www.udemy.com/course/learn-flutter-dart-to-build-ios-android-apps/
https://www.youtube.com/c/ATNSTUDIO
https://www.youtube.com/c/TheFlutterWay
```

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://flutter.dev/docs/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://flutter.dev/docs/cookbook)

For help getting started with Flutter, view our
[online documentation](https://flutter.dev/docs), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
