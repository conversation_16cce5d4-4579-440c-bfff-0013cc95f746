[{"title": "Avengers: Endgame", "originalTitle": "Avengers: Endgame", "overview": "After the devastating events of Avengers: Infinity War, the universe is in ruins due to the efforts of the Mad Titan, <PERSON><PERSON>. With the help of remaining allies, the Avengers must assemble once more in order to undo <PERSON><PERSON>' actions and restore order to the universe once and for all, no matter what consequences may be in store.", "genres": "Action, Adventure, Drama", "releaseDate": "2019-04-26", "runtime": 181, "voteAverage": 8.4, "ageRating": "PG-13", "language": "English", "country": "USA", "director": "<PERSON>, <PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": true, "isSplashBanner": false, "bannerOrder": 1}, {"title": "Spider-Man: No Way Home", "originalTitle": "Spider-Man: No Way Home", "overview": "<PERSON> is unmasked and no longer able to separate his normal life from the high-stakes of being a super-hero. When he asks for help from <PERSON> the stakes become even more dangerous, forcing him to discover what it truly means to be Spider-<PERSON>.", "genres": "Action, Adventure, Fantasy", "releaseDate": "2021-12-17", "runtime": 148, "voteAverage": 8.2, "ageRating": "PG-13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": true, "isSplashBanner": true, "bannerOrder": 2}, {"title": "Top Gun: <PERSON><PERSON><PERSON>", "originalTitle": "Top Gun: <PERSON><PERSON><PERSON>", "overview": "After more than thirty years of service as one of the Navy's top aviators, and dodging the advancement in rank that would ground him, <PERSON>' <PERSON> finds himself training a detachment of TOP GUN graduates for a specialized mission the likes of which no living pilot has ever seen.", "genres": "Action, Drama", "releaseDate": "2022-05-27", "runtime": 130, "voteAverage": 8.3, "ageRating": "PG-13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false}, {"title": "Avatar: The Way of Water", "originalTitle": "Avatar: The Way of Water", "overview": "Set more than a decade after the events of the first film, Avatar: The Way of Water begins to tell the story of the <PERSON><PERSON> family, the trouble that follows them, the lengths they go to keep each other safe, the battles they fight to stay alive, and the tragedies they endure.", "genres": "Action, Adventure, Fantasy", "releaseDate": "2022-12-16", "runtime": 192, "voteAverage": 7.6, "ageRating": "PG-13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": true, "bannerOrder": 3}, {"title": "Black Panther: Wakanda Forever", "originalTitle": "Black Panther: Wakanda Forever", "overview": "Queen <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and the <PERSON> fight to protect their nation from intervening world powers in the wake of King <PERSON>'s death. As the Wakandans strive to embrace their next chapter, the heroes must band together with the help of <PERSON> <PERSON> and <PERSON> and forge a new path for the kingdom of Wakanda.", "genres": "Action, Adventure, Drama", "releaseDate": "2022-11-11", "runtime": 161, "voteAverage": 7.3, "ageRating": "PG-13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false}]