import 'package:get/get.dart';
import '../models/bug_report_model.dart';
import '../services/bug_report_service.dart';
import 'auth_controller.dart';

class BugReportController extends GetxController {
  final BugReportService _bugReportService = BugReportService();
  final AuthController _authController = Get.find<AuthController>();

  final RxList<BugReportModel> _allBugReports = <BugReportModel>[].obs;
  final RxList<BugReportModel> _userBugReports = <BugReportModel>[].obs;
  final RxList<BugReportModel> _filteredBugReports = <BugReportModel>[].obs;

  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final Rx<BugReportModel?> _selectedBugReport = Rx<BugReportModel?>(null);
  final RxString _statusFilter = BugStatus.pending.name.obs;

  // Getters
  List<BugReportModel> get allBugReports => _allBugReports;
  List<BugReportModel> get userBugReports => _userBugReports;
  List<BugReportModel> get filteredBugReports => _filteredBugReports;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  BugReportModel? get selectedBugReport => _selectedBugReport.value;
  String get statusFilter => _statusFilter.value;

  @override
  void onInit() {
    super.onInit();
    fetchBugReports();
  }

  // Lấy tất cả báo cáo lỗi
  Future<void> fetchBugReports() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      if (_authController.isAdmin) {
        // Admin và developer có thể xem tất cả báo cáo
        _allBugReports.value = await _bugReportService.getAllBugReports();
        _applyStatusFilter();
      } else {
        // Người dùng thường chỉ xem báo cáo của họ
        if (_authController.user?.id != null) {
          _userBugReports.value = await _bugReportService.getUserBugReports(
            _authController.user!.id!,
          );
          _filteredBugReports.value = _userBugReports;
        }
      }
    } catch (e) {
      _errorMessage.value = 'Không thể tải báo cáo lỗi: $e';
    } finally {
      _isLoading.value = false;
    }
  }

  // Lọc báo cáo theo trạng thái
  void setStatusFilter(BugStatus status) {
    _statusFilter.value = status.name;
    _applyStatusFilter();
  }

  // Áp dụng bộ lọc trạng thái
  void _applyStatusFilter() {
    if (_statusFilter.value == 'all') {
      _filteredBugReports.value = _allBugReports;
    } else {
      _filteredBugReports.value = _allBugReports
          .where((report) => report.status.name == _statusFilter.value)
          .toList();
    }
  }

  // Tạo báo cáo lỗi mới
  Future<bool> createBugReport({
    required String title,
    required String description,
    Map<String, dynamic>? additionalData,
  }) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      if (_authController.user == null) {
        _errorMessage.value = 'Bạn cần đăng nhập để báo cáo lỗi';
        return false;
      }

      final bugReport = await _bugReportService.createBugReport(
        title: title,
        description: description,
        reportedBy: _authController.user!.id!,
        reportedByName: _authController.user!.name ?? 'Unknown User',
        reportedByEmail: _authController.user!.email,
        additionalData: additionalData,
      );

      if (bugReport != null) {
        // Thêm vào danh sách báo cáo của người dùng
        _userBugReports.add(bugReport);
        return true;
      } else {
        _errorMessage.value = 'Không thể tạo báo cáo lỗi';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tạo báo cáo: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Thêm phản hồi cho báo cáo lỗi
  Future<bool> addResponse({
    required String bugReportId,
    required String message,
    BugStatus? newStatus,
  }) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      if (_authController.user == null) {
        _errorMessage.value = 'Bạn cần đăng nhập để phản hồi';
        return false;
      }

      final isAdmin = _authController.isAdmin;
      final isDeveloper = _authController.userRole == 'developer';

      final success = await _bugReportService.addResponse(
        bugReportId: bugReportId,
        responderId: _authController.user!.id!,
        responderName: _authController.user!.name ?? 'Unknown User',
        message: message,
        isFromAdmin: isAdmin && !isDeveloper,
        isFromDeveloper: isDeveloper,
        newStatus: newStatus,
      );

      if (success) {
        // Cập nhật lại báo cáo lỗi đã chọn
        await fetchBugReportDetail(bugReportId);
        return true;
      } else {
        _errorMessage.value = 'Không thể thêm phản hồi';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi thêm phản hồi: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Cập nhật trạng thái báo cáo lỗi
  Future<bool> updateBugStatus({
    required String bugReportId,
    required BugStatus newStatus,
  }) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      if (_authController.user == null) {
        _errorMessage.value = 'Bạn cần đăng nhập để cập nhật trạng thái';
        return false;
      }

      if (!_authController.isAdmin) {
        _errorMessage.value = 'Bạn không có quyền cập nhật trạng thái';
        return false;
      }

      final isDeveloper = _authController.userRole == 'developer';

      final success = await _bugReportService.updateBugStatus(
        bugReportId: bugReportId,
        newStatus: newStatus,
        updatedBy: _authController.user!.id!,
        updatedByName: _authController.user!.name ?? 'Unknown User',
        isFromDeveloper: isDeveloper,
      );

      if (success) {
        // Cập nhật lại báo cáo lỗi đã chọn
        await fetchBugReportDetail(bugReportId);
        return true;
      } else {
        _errorMessage.value = 'Không thể cập nhật trạng thái';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi cập nhật trạng thái: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Lấy chi tiết báo cáo lỗi
  Future<void> fetchBugReportDetail(String bugReportId) async {
    // Đặt giá trị loading trước
    _isLoading.value = true;
    _errorMessage.value = '';

    // Đặt giá trị ban đầu là null để tránh lỗi khi cập nhật trong quá trình build
    _selectedBugReport.value = null;

    try {
      // Tìm trong danh sách hiện có
      BugReportModel? report;

      // Sử dụng Future.delayed để đảm bảo UI được cập nhật trước khi tiếp tục
      await Future.delayed(Duration.zero);

      if (_authController.isAdmin) {
        report = _allBugReports.firstWhereOrNull((r) => r.id == bugReportId);

        // Nếu không tìm thấy, tải lại tất cả báo cáo
        if (report == null) {
          await fetchBugReports();
          report = _allBugReports.firstWhereOrNull((r) => r.id == bugReportId);
        }
      } else {
        report = _userBugReports.firstWhereOrNull((r) => r.id == bugReportId);

        // Nếu không tìm thấy, tải lại báo cáo của người dùng
        if (report == null && _authController.user?.id != null) {
          _userBugReports.value = await _bugReportService.getUserBugReports(
            _authController.user!.id!,
          );
          report = _userBugReports.firstWhereOrNull((r) => r.id == bugReportId);
        }
      }

      // Cập nhật giá trị sau khi đã hoàn thành tất cả các thao tác
      _selectedBugReport.value = report;

      if (report == null) {
        _errorMessage.value = 'Không tìm thấy báo cáo lỗi';
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tải chi tiết báo cáo: $e';
    } finally {
      _isLoading.value = false;
    }
  }

  // Xóa thông báo lỗi
  void clearError() {
    _errorMessage.value = '';
  }
}
