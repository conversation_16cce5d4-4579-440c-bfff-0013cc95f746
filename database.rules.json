{"rules": {".read": "auth != null", ".write": "auth != null && (root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", "notifications": {".read": "auth != null", ".write": "auth != null && (root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", "$notification_id": {".read": "auth != null", ".write": "auth != null && (root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", ".validate": "newData.hasChildren(['title', 'body', 'createdAt', 'expiresAt', 'isPublic'])"}}, "user_notifications": {"$userId": {".read": "auth != null && auth.uid == $userId", ".write": "auth != null && auth.uid == $userId"}}, "bug_reports": {".read": "auth != null", "$bugReportId": {".write": "auth != null && (data.child('reportedBy').val() == auth.uid || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')"}}, "chat_support": {"$chatId": {".read": "auth != null && (data.child('participants').child(auth.uid).exists() || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", ".write": "auth != null && (data.child('participants').child(auth.uid).exists() || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", "messages": {"$messageId": {".write": "auth != null && (data.parent().parent().child('participants').child(auth.uid).exists() || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')"}}}}, "seat_reservations": {".read": "auth != null", ".write": "auth != null"}, "user_roles": {"$userId": {".read": "auth != null && (auth.uid == $userId || root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer')", ".write": "root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer'"}}, "fcm_tokens": {"$userId": {".read": "auth != null && auth.uid == $userId", ".write": "auth != null && auth.uid == $userId"}}, "analytics": {".read": "root.child('user_roles').child(auth.uid).child('role').val() == 'admin' || root.child('user_roles').child(auth.uid).child('role').val() == 'developer'", ".write": "auth != null"}}}