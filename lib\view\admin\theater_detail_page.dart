import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/theater_model.dart';
import '../../models/screen_model.dart' as screen_models;
import '../../services/theater_service.dart';
import '../../services/screen_service.dart';
// import 'edit_theater_page.dart'; // TODO: Create this file
import 'screen_management_page.dart';

class TheaterDetailPage extends StatefulWidget {
  final TheaterModel theater;

  const TheaterDetailPage({Key? key, required this.theater}) : super(key: key);

  @override
  State<TheaterDetailPage> createState() => _TheaterDetailPageState();
}

class _TheaterDetailPageState extends State<TheaterDetailPage> {
  final TheaterService _theaterService = TheaterService();
  final ScreenService _screenService = ScreenService();
  final RxList<screen_models.ScreenModel> _screens =
      <screen_models.ScreenModel>[].obs;
  final RxBool _isLoading = false.obs;
  late Rx<TheaterModel> _theater;

  @override
  void initState() {
    super.initState();
    _theater = widget.theater.obs;
    _loadScreens();
  }

  Future<void> _loadScreens() async {
    try {
      _isLoading.value = true;
      final screens = await _screenService
          .getScreensByTheater(_theater.value.id, activeOnly: false);
      _screens.value = screens;
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể tải danh sách phòng chiếu: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _toggleTheaterStatus() async {
    try {
      final updatedTheater = _theater.value.copyWith(
        isActive: !_theater.value.isActive,
        updatedAt: DateTime.now(),
      );

      await _theaterService.updateTheater(updatedTheater);
      _theater.value = updatedTheater;

      Get.snackbar(
        'Thành công',
        'Đã cập nhật trạng thái rạp',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withOpacity(0.7),
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể cập nhật trạng thái: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    }
  }

  Future<void> _deleteTheater() async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        backgroundColor: const Color(0xff2B5876),
        title: Text(
          'Xác nhận xóa',
          style: GoogleFonts.mulish(color: Colors.white),
        ),
        content: Text(
          'Bạn có chắc chắn muốn xóa rạp này? Hành động này không thể hoàn tác.',
          style: GoogleFonts.mulish(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(
              'Hủy',
              style: GoogleFonts.mulish(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text(
              'Xóa',
              style: GoogleFonts.mulish(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _theaterService.deleteTheater(_theater.value.id);
        Get.back();
        Get.snackbar(
          'Thành công',
          'Đã xóa rạp thành công',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.withOpacity(0.7),
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'Lỗi',
          'Không thể xóa rạp: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withOpacity(0.7),
          colorText: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Chi Tiết Rạp',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    PopupMenuButton<String>(
                      icon: const Icon(Icons.more_vert, color: Colors.white),
                      color: const Color(0xff2B5876),
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            // TODO: Implement EditTheaterPage
                            Get.snackbar(
                                'Thông báo', 'Tính năng sẽ được cập nhật sớm');
                            break;
                          case 'toggle_status':
                            _toggleTheaterStatus();
                            break;
                          case 'delete':
                            _deleteTheater();
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              const Icon(Icons.edit, color: Colors.white),
                              const SizedBox(width: 8),
                              Text(
                                'Chỉnh sửa',
                                style: GoogleFonts.mulish(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'toggle_status',
                          child: Row(
                            children: [
                              Icon(
                                _theater.value.isActive
                                    ? Icons.pause
                                    : Icons.play_arrow,
                                color: Colors.white,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _theater.value.isActive
                                    ? 'Tạm dừng'
                                    : 'Kích hoạt',
                                style: GoogleFonts.mulish(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              const Icon(Icons.delete, color: Colors.red),
                              const SizedBox(width: 8),
                              Text(
                                'Xóa',
                                style: GoogleFonts.mulish(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Obx(() => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Theater Info Card
                          _buildInfoCard(),
                          const SizedBox(height: 16),

                          // Address Card
                          _buildAddressCard(),
                          const SizedBox(height: 16),

                          // Facilities Card
                          _buildFacilitiesCard(),
                          const SizedBox(height: 16),

                          // Operating Hours Card
                          _buildOperatingHoursCard(),
                          const SizedBox(height: 16),

                          // Screens Section
                          _buildScreensSection(),
                          const SizedBox(height: 16),
                        ],
                      )),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0x1EA6A1E0), Color(0x1EA1F3FE)],
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: const Icon(
                  Icons.movie,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _theater.value.name,
                      style: GoogleFonts.mulish(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _theater.value.isActive
                            ? Colors.green.withOpacity(0.2)
                            : Colors.red.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _theater.value.isActive
                              ? Colors.green
                              : Colors.red,
                        ),
                      ),
                      child: Text(
                        _theater.value.isActive ? 'Hoạt động' : 'Tạm dừng',
                        style: GoogleFonts.mulish(
                          fontSize: 12,
                          color: _theater.value.isActive
                              ? Colors.green
                              : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Icon(Icons.phone, color: Colors.white70, size: 16),
              const SizedBox(width: 8),
              Text(
                _theater.value.phoneNumber,
                style: GoogleFonts.mulish(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
          if (_theater.value.email != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.email, color: Colors.white70, size: 16),
                const SizedBox(width: 8),
                Text(
                  _theater.value.email!,
                  style: GoogleFonts.mulish(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAddressCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.location_on, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                'Địa chỉ',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _theater.value.address.fullAddress,
            style: GoogleFonts.mulish(
              fontSize: 14,
              color: Colors.white70,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFacilitiesCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.star, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                'Tiện ích',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (_theater.value.facilities.isEmpty)
            Text(
              'Chưa có thông tin tiện ích',
              style: GoogleFonts.mulish(
                fontSize: 14,
                color: Colors.white70,
              ),
            )
          else
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _theater.value.facilities
                  .map(
                    (facility) => Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue.withOpacity(0.5)),
                      ),
                      child: Text(
                        facility,
                        style: GoogleFonts.mulish(
                          fontSize: 12,
                          color: Colors.blue[200],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ),
        ],
      ),
    );
  }

  Widget _buildOperatingHoursCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.access_time, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                'Giờ hoạt động',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (_theater.value.operatingHours.isEmpty)
            Text(
              'Chưa có thông tin giờ hoạt động',
              style: GoogleFonts.mulish(
                fontSize: 14,
                color: Colors.white70,
              ),
            )
          else
            Column(
              children: _theater.value.operatingHours.entries
                  .map(
                    (entry) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _getDayName(entry.key),
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                          Text(
                            '${entry.value.open} - ${entry.value.close}',
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                  .toList(),
            ),
        ],
      ),
    );
  }

  Widget _buildScreensSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Icon(Icons.tv, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Phòng chiếu',
                    style: GoogleFonts.mulish(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: _loadScreens,
                    icon: const Icon(Icons.refresh,
                        color: Colors.white70, size: 20),
                    tooltip: 'Làm mới',
                  ),
                  TextButton(
                    onPressed: () => Get.to(() =>
                            ScreenManagementPage(theaterId: _theater.value.id))
                        ?.then((_) =>
                            _loadScreens()), // Refresh screens when returning
                    child: Text(
                      'Quản lý',
                      style: GoogleFonts.mulish(
                        fontSize: 14,
                        color: Colors.blue[200],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          Obx(() {
            if (_isLoading.value) {
              return const Center(
                child: CircularProgressIndicator(color: Colors.white),
              );
            }

            if (_screens.isEmpty) {
              return Text(
                'Chưa có phòng chiếu nào',
                style: GoogleFonts.mulish(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              );
            }

            return Column(
              children: _screens
                  .take(3)
                  .map(
                    (screen) => Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(8),
                        border:
                            Border.all(color: Colors.white.withOpacity(0.1)),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: Colors.purple.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: const Icon(
                              Icons.tv,
                              color: Colors.purple,
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  screen.name,
                                  style: GoogleFonts.mulish(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white,
                                  ),
                                ),
                                Text(
                                  '${screen.totalSeats} ghế • ${screen.type.displayName}',
                                  style: GoogleFonts.mulish(
                                    fontSize: 12,
                                    color: Colors.white70,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                  .toList(),
            );
          }),
          if (_screens.length > 3) ...[
            const SizedBox(height: 8),
            Text(
              'Và ${_screens.length - 3} phòng chiếu khác...',
              style: GoogleFonts.mulish(
                fontSize: 12,
                color: Colors.white70,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _getDayName(String day) {
    switch (day.toLowerCase()) {
      case 'monday':
        return 'Thứ 2';
      case 'tuesday':
        return 'Thứ 3';
      case 'wednesday':
        return 'Thứ 4';
      case 'thursday':
        return 'Thứ 5';
      case 'friday':
        return 'Thứ 6';
      case 'saturday':
        return 'Thứ 7';
      case 'sunday':
        return 'Chủ nhật';
      default:
        return day;
    }
  }
}
