import 'dart:developer' as developer;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'storage_service.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final StorageService _storageService = StorageService();

  // Getter cho FirebaseFirestore để sử dụng bên ngoài
  FirebaseFirestore get firestore => _firestore;

  // Collection names
  static const String usersCollection = 'users';
  static const String userRolesCollection = 'user_roles';

  // Cache for user data to reduce Firestore calls
  UserModel? _cachedUser;
  String? _cachedUserId;

  // Helper method to log errors in debug mode
  void _logError(String message, dynamic error) {
    if (kDebugMode) {
      developer.log('$message: $error', name: 'AuthService');
    }
  }

  // Clear cache when user logs out
  void _clearCache() {
    _cachedUser = null;
    _cachedUserId = null;
  }

  Future<UserModel?> login(String email, String password) async {
    try {
      final UserCredential userCredential =
          await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userCredential.user == null) {
        return null;
      }

      UserModel user;

      try {
        // Try to get user data from Firestore
        final doc = await _firestore
            .collection(usersCollection)
            .doc(userCredential.user!.uid)
            .get();

        UserRole role = UserRole.user; // Default role

        // Check if user has a role in user_roles collection
        try {
          final roleDoc = await _firestore
              .collection(userRolesCollection)
              .doc(userCredential.user!.uid)
              .get();

          if (roleDoc.exists) {
            final roleData = roleDoc.data() as Map<String, dynamic>;
            role = UserRoleExtension.fromString(roleData['role']);
          }
        } catch (roleError) {
          _logError('Error fetching user role', roleError);
        }

        if (doc.exists) {
          final data = doc.data() as Map<String, dynamic>;
          user = UserModel(
            id: userCredential.user!.uid,
            email: userCredential.user!.email,
            name: data['name'] ?? email.split('@')[0],
            photoUrl: data['photoUrl'] ??
                'https://ui-avatars.com/api/?name=${email.split('@')[0]}',
            role: role,
          );
        } else {
          // Create a new user model
          user = UserModel(
            id: userCredential.user!.uid,
            email: userCredential.user!.email,
            name: email.split('@')[0],
            photoUrl: 'https://ui-avatars.com/api/?name=${email.split('@')[0]}',
            role: role,
          );

          // Try to save to Firestore
          try {
            await _firestore
                .collection(usersCollection)
                .doc(userCredential.user!.uid)
                .set(user.toJson());

            // Also save role information
            await _firestore
                .collection(userRolesCollection)
                .doc(userCredential.user!.uid)
                .set({
              'role': role.name,
              'updatedAt': FieldValue.serverTimestamp()
            });
          } catch (firestoreError) {
            // Log Firestore error but continue with local storage
            _logError('Error saving user to Firestore', firestoreError);
          }
        }
      } catch (firestoreError) {
        // If Firestore access fails, create a basic user model from auth data
        _logError('Error accessing Firestore', firestoreError);

        // Create a basic user model from Firebase Auth data
        user = UserModel(
          id: userCredential.user!.uid,
          email: userCredential.user!.email,
          name: email.split('@')[0],
          photoUrl: 'https://ui-avatars.com/api/?name=${email.split('@')[0]}',
          role: UserRole
              .user, // Default role for users created without Firestore access
        );
      }

      try {
        // Clear any existing user data first to avoid conflicts
        await _storageService.clearUserData();
        // Then save the new user data
        await _storageService.saveUser(user);
        await _storageService.setLoggedIn(true);
      } catch (storageError) {
        // If there's an error saving to storage, we can still return the user
        // but log the error
        _logError('Error saving user to storage during login', storageError);
      }

      return user;
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found') {
        throw Exception('No user found for that email.');
      } else if (e.code == 'wrong-password') {
        throw Exception('Wrong password provided for that user.');
      } else {
        throw Exception(e.message ?? 'Authentication failed');
      }
    } catch (e) {
      throw Exception('Login error: $e');
    }
  }

  Future<UserModel?> register(
      String email, String password, String name) async {
    try {
      final UserCredential userCredential =
          await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userCredential.user == null) {
        return null;
      }

      // Default role for new users is 'user'
      const UserRole role = UserRole.user;

      final user = UserModel(
        id: userCredential.user!.uid,
        email: userCredential.user!.email,
        name: name,
        photoUrl: 'https://ui-avatars.com/api/?name=$name',
        role: role,
      );

      // Try to save to Firestore, but continue even if it fails
      try {
        // Save user data
        await _firestore
            .collection(usersCollection)
            .doc(userCredential.user!.uid)
            .set(user.toJson());

        // Save role information
        await _firestore
            .collection(userRolesCollection)
            .doc(userCredential.user!.uid)
            .set({
          'role': role.name,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp()
        });
      } catch (firestoreError) {
        // Log Firestore error but continue with local storage
        _logError('Error saving user to Firestore during registration',
            firestoreError);
      }

      try {
        // Clear any existing user data first to avoid conflicts
        await _storageService.clearUserData();
        // Then save the new user data
        await _storageService.saveUser(user);
        await _storageService.setLoggedIn(true);
      } catch (storageError) {
        // If there's an error saving to storage, we can still return the user
        // but log the error
        _logError(
            'Error saving user to storage during registration', storageError);
      }

      return user;
    } on FirebaseAuthException catch (e) {
      if (e.code == 'weak-password') {
        throw Exception('The password provided is too weak.');
      } else if (e.code == 'email-already-in-use') {
        throw Exception('The account already exists for that email.');
      } else {
        throw Exception(e.message ?? 'Registration failed');
      }
    } catch (e) {
      throw Exception('Registration error: $e');
    }
  }

  Future<void> logout() async {
    try {
      await _auth.signOut();
      await _storageService.setLoggedIn(false);
      await _storageService.clearAll();

      // Clear cache
      _clearCache();
    } catch (e) {
      throw Exception('Logout error: $e');
    }
  }

  Future<bool> isLoggedIn() async {
    return _auth.currentUser != null;
  }

  // Get the current Firebase user
  User? getCurrentFirebaseUser() {
    return _auth.currentUser;
  }

  Future<UserModel?> getCurrentUser() async {
    try {
      final User? firebaseUser = _auth.currentUser;

      if (firebaseUser == null) {
        return null;
      }

      // Check cache first
      if (_cachedUser != null && _cachedUserId == firebaseUser.uid) {
        return _cachedUser;
      }

      // First try to get user from local storage
      try {
        final localUser = await _storageService.getUser();
        if (localUser != null) {
          // Update cache
          _cachedUser = localUser;
          _cachedUserId = firebaseUser.uid;
          return localUser;
        }
      } catch (e) {
        // If there's an error getting the user from storage, continue with fetching from Firestore
        _logError('Error getting user from storage', e);
      }

      // Try to get user from Firestore
      try {
        final doc = await _firestore
            .collection(usersCollection)
            .doc(firebaseUser.uid)
            .get();

        UserRole role = UserRole.user; // Default role

        // Check if user has a role in user_roles collection
        try {
          final roleDoc = await _firestore
              .collection(userRolesCollection)
              .doc(firebaseUser.uid)
              .get();

          if (roleDoc.exists) {
            final roleData = roleDoc.data() as Map<String, dynamic>;
            role = UserRoleExtension.fromString(roleData['role']);
          }
        } catch (roleError) {
          _logError('Error fetching user role', roleError);
        }

        if (doc.exists) {
          final data = doc.data() as Map<String, dynamic>;
          final user = UserModel(
            id: firebaseUser.uid,
            email: firebaseUser.email,
            name: data['name'] ?? firebaseUser.email?.split('@')[0],
            photoUrl: data['photoUrl'] ??
                'https://ui-avatars.com/api/?name=${firebaseUser.email?.split('@')[0]}',
            role: role,
          );
          try {
            await _storageService.saveUser(user);
          } catch (storageError) {
            // If there's an error saving to storage, we can still return the user
            _logError(
                'Error saving user to storage in getCurrentUser', storageError);
          }

          // Update cache
          _cachedUser = user;
          _cachedUserId = firebaseUser.uid;

          return user;
        }
      } catch (firestoreError) {
        // If Firestore access fails, create a basic user model from auth data
        _logError(
            'Error accessing Firestore in getCurrentUser', firestoreError);
      }

      // If we couldn't get the user from Firestore, create a basic user from auth data
      final basicUser = UserModel(
        id: firebaseUser.uid,
        email: firebaseUser.email,
        name: firebaseUser.email?.split('@')[0] ?? 'User',
        photoUrl:
            'https://ui-avatars.com/api/?name=${firebaseUser.email?.split('@')[0] ?? 'User'}',
        role: UserRole.user, // Default role
      );

      try {
        await _storageService.saveUser(basicUser);
      } catch (storageError) {
        _logError('Error saving basic user to storage', storageError);
      }

      // Update cache
      _cachedUser = basicUser;
      _cachedUserId = firebaseUser.uid;

      return basicUser;
    } catch (e) {
      throw Exception('Get current user error: $e');
    }
  }

  // Check if a user is an admin
  Future<bool> isUserAdmin(String userId) async {
    try {
      final roleDoc =
          await _firestore.collection(userRolesCollection).doc(userId).get();

      if (roleDoc.exists) {
        final roleData = roleDoc.data() as Map<String, dynamic>;
        final role = roleData['role'] as String?;
        return role == UserRole.admin.name || role == UserRole.developer.name;
      }

      return false;
    } catch (e) {
      _logError('Error checking admin status', e);
      return false;
    }
  }

  // Check if a user is a developer
  Future<bool> isUserDeveloper(String userId) async {
    try {
      final roleDoc =
          await _firestore.collection(userRolesCollection).doc(userId).get();

      if (roleDoc.exists) {
        final roleData = roleDoc.data() as Map<String, dynamic>;
        return roleData['role'] == UserRole.developer.name;
      }

      return false;
    } catch (e) {
      _logError('Error checking developer status', e);
      return false;
    }
  }

  // Set a user's role to admin
  Future<bool> setUserAsAdmin(String userId) async {
    return await _updateUserRole(userId, UserRole.admin);
  }

  // Set a user's role to regular user
  Future<bool> setUserAsRegular(String userId) async {
    return await _updateUserRole(userId, UserRole.user);
  }

  // Set a user's role to developer
  Future<bool> setUserAsDeveloper(String userId) async {
    return await _updateUserRole(userId, UserRole.developer);
  }

  // Helper method to update user role
  Future<bool> _updateUserRole(String userId, UserRole role) async {
    try {
      // Update role in user_roles collection
      await _firestore.collection(userRolesCollection).doc(userId).set(
          {'role': role.name, 'updatedAt': FieldValue.serverTimestamp()},
          SetOptions(merge: true));

      // Also update role in users collection
      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .update({'role': role.name});

      return true;
    } catch (e) {
      _logError('Error updating user role', e);
      return false;
    }
  }

  // Get all users with their roles
  Future<List<UserModel>> getAllUsers() async {
    try {
      final snapshot = await _firestore.collection(usersCollection).get();

      if (snapshot.docs.isEmpty) {
        return [];
      }

      final List<UserModel> users = [];

      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          final user = UserModel.fromJson({...data, 'id': doc.id});
          users.add(user);
        } catch (e) {
          _logError('Error parsing user document', e);
        }
      }

      return users;
    } catch (e) {
      _logError('Error getting all users', e);
      return [];
    }
  }

  // Update user profile information
  Future<bool> updateUserProfile(String userId,
      {String? name, String? photoUrl}) async {
    try {
      final updateData = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (name != null) {
        updateData['name'] = name;
      }

      if (photoUrl != null) {
        updateData['photoUrl'] = photoUrl;
      }

      // Update in Firestore
      await _firestore
          .collection(usersCollection)
          .doc(userId)
          .update(updateData);

      // Get the updated user data
      final doc =
          await _firestore.collection(usersCollection).doc(userId).get();

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;

        // Get user role
        UserRole role = UserRole.user;
        try {
          final roleDoc = await _firestore
              .collection(userRolesCollection)
              .doc(userId)
              .get();

          if (roleDoc.exists) {
            final roleData = roleDoc.data() as Map<String, dynamic>;
            role = UserRoleExtension.fromString(roleData['role']);
          }
        } catch (roleError) {
          _logError(
              'Error fetching user role during profile update', roleError);
        }

        // Create updated user model
        final updatedUser = UserModel(
          id: userId,
          email: data['email'],
          name: data['name'],
          photoUrl: data['photoUrl'],
          role: role,
        );

        // Update in local storage
        try {
          await _storageService.saveUser(updatedUser);
        } catch (storageError) {
          _logError('Error saving updated user to storage', storageError);
        }
      }

      return true;
    } catch (e) {
      _logError('Error updating user profile', e);
      return false;
    }
  }
}
