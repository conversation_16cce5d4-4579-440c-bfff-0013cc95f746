import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/bug_report_model.dart';
import '../models/notification_model.dart';
import 'notification_service.dart';

class BugReportService {
  // Sử dụng instance của Firestore
  final NotificationService _notificationService = NotificationService();

  final CollectionReference _bugReportsCollection;

  BugReportService()
      : _bugReportsCollection =
            FirebaseFirestore.instance.collection('bug_reports');

  Future<List<BugReportModel>> getAllBugReports() async {
    try {
      final querySnapshot = await _bugReportsCollection
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => BugReportModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting all bug reports: $e');
      return [];
    }
  }

  Future<List<BugReportModel>> getBugReportsByStatus(BugStatus status) async {
    try {
      final querySnapshot = await _bugReportsCollection
          .where('status', isEqualTo: status.name)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => BugReportModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting bug reports by status: $e');
      return [];
    }
  }

  Future<List<BugReportModel>> getUserBugReports(String userId) async {
    try {
      final querySnapshot = await _bugReportsCollection
          .where('reportedBy', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => BugReportModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting user bug reports: $e');
      return [];
    }
  }

  Future<BugReportModel?> createBugReport({
    required String title,
    required String description,
    required String reportedBy,
    required String reportedByName,
    String? reportedByEmail,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final bugReport = BugReportModel(
        id: '',
        title: title,
        description: description,
        reportedBy: reportedBy,
        reportedByName: reportedByName,
        reportedByEmail: reportedByEmail,
        createdAt: DateTime.now(),
        status: BugStatus.pending,
        responses: [],
        additionalData: additionalData,
      );

      // Lưu vào Firestore
      try {
        final docRef = await _bugReportsCollection.add(bugReport.toFirestore());
        final newBugReport = bugReport.copyWith(id: docRef.id);

        // Tạo phản hồi tự động
        await addResponse(
          bugReportId: docRef.id,
          responderId: 'system',
          responderName: 'Hệ thống',
          message:
              'Cảm ơn bạn đã báo lỗi. Đội ngũ của chúng tôi đang xem xét và sẽ phản hồi sớm.',
          isFromAdmin: false,
          isFromDeveloper: false,
        );

        // Gửi thông báo cho người dùng
        await _notificationService.createNotification(
          title: 'Cảm ơn bạn đã báo lỗi',
          body:
              'Đội ngũ của chúng tôi đang xem xét báo cáo lỗi của bạn và sẽ phản hồi sớm.',
          targetScreen: 'bug_report_detail',
          data: {'bugReportId': docRef.id},
          isPublic: false,
          targetUserIds: [reportedBy],
        );

        // Gửi thông báo cho admin và developer
        await _notifyAdminAndDevelopers(
          title: 'Báo cáo lỗi mới',
          body: 'Người dùng $reportedByName đã báo cáo lỗi: $title',
          bugReportId: docRef.id,
        );

        return newBugReport;
      } catch (firestoreError) {
        print('Error writing bug report to Firestore: $firestoreError');

        // Trả về đối tượng với ID giả
        final id = DateTime.now().millisecondsSinceEpoch.toString();
        return bugReport.copyWith(id: 'local-$id');
      }
    } catch (e) {
      print('Error creating bug report: $e');
      return null;
    }
  }

  // Thêm phản hồi cho báo cáo lỗi
  Future<bool> addResponse({
    required String bugReportId,
    required String responderId,
    required String responderName,
    required String message,
    bool isFromAdmin = false,
    bool isFromDeveloper = false,
    BugStatus? newStatus,
  }) async {
    try {
      // Lấy báo cáo lỗi hiện tại
      final docSnapshot = await _bugReportsCollection.doc(bugReportId).get();

      if (!docSnapshot.exists) {
        print('Bug report not found: $bugReportId');
        return false;
      }

      final bugReport = BugReportModel.fromFirestore(docSnapshot);

      // Tạo phản hồi mới
      final response = BugResponseModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        responderId: responderId,
        responderName: responderName,
        message: message,
        createdAt: DateTime.now(),
        isFromAdmin: isFromAdmin,
        isFromDeveloper: isFromDeveloper,
        newStatus: newStatus,
      );

      // Thêm phản hồi vào danh sách
      final updatedResponses = [...bugReport.responses, response];

      // Cập nhật trạng thái nếu có
      final updatedStatus = newStatus ?? bugReport.status;

      // Cập nhật báo cáo lỗi
      await _bugReportsCollection.doc(bugReportId).update({
        'responses': updatedResponses.map((r) => r.toMap()).toList(),
        'status': updatedStatus.name,
      });

      // Gửi thông báo cho người báo cáo
      if (isFromAdmin || isFromDeveloper) {
        await _notificationService.createNotification(
          title: 'Phản hồi cho báo cáo lỗi của bạn',
          body: message,
          targetScreen: 'bug_report_detail',
          data: {'bugReportId': bugReportId},
          isPublic: false,
          targetUserIds: [bugReport.reportedBy],
        );
      }

      // Nếu developer thay đổi trạng thái, thông báo cho admin
      if (isFromDeveloper && newStatus != null) {
        await _notifyAdmins(
          title: 'Cập nhật trạng thái báo cáo lỗi',
          body:
              '$responderName đã cập nhật trạng thái báo cáo lỗi "${bugReport.title}" thành ${newStatus.displayName}',
          bugReportId: bugReportId,
        );
      }

      return true;
    } catch (e) {
      print('Error adding response: $e');
      return false;
    }
  }

  // Cập nhật trạng thái báo cáo lỗi
  Future<bool> updateBugStatus({
    required String bugReportId,
    required BugStatus newStatus,
    required String updatedBy,
    required String updatedByName,
    bool isFromDeveloper = false,
  }) async {
    try {
      // Cập nhật trạng thái
      await _bugReportsCollection.doc(bugReportId).update({
        'status': newStatus.name,
      });

      // Thêm phản hồi về việc thay đổi trạng thái
      await addResponse(
        bugReportId: bugReportId,
        responderId: updatedBy,
        responderName: updatedByName,
        message: 'Đã cập nhật trạng thái thành: ${newStatus.displayName}',
        isFromAdmin: !isFromDeveloper,
        isFromDeveloper: isFromDeveloper,
        newStatus: newStatus,
      );

      return true;
    } catch (e) {
      print('Error updating bug status: $e');
      return false;
    }
  }

  // Gửi thông báo cho admin và developer
  Future<void> _notifyAdminAndDevelopers({
    required String title,
    required String body,
    required String bugReportId,
  }) async {
    try {
      await _notificationService.createNotification(
        title: title,
        body: body,
        targetScreen: 'bug_report_detail',
        data: {'bugReportId': bugReportId, 'isAdminNotification': 'true'},
        isPublic: true, // Hiển thị cho tất cả admin và developer
      );
    } catch (e) {
      print('Error notifying admins and developers: $e');
    }
  }

  // Gửi thông báo cho admin
  Future<void> _notifyAdmins({
    required String title,
    required String body,
    required String bugReportId,
  }) async {
    try {
      await _notificationService.createNotification(
        title: title,
        body: body,
        targetScreen: 'bug_report_detail',
        data: {'bugReportId': bugReportId, 'isAdminNotification': 'true'},
        isPublic: true, // Hiển thị cho tất cả admin
      );
    } catch (e) {
      print('Error notifying admins: $e');
    }
  }
}
