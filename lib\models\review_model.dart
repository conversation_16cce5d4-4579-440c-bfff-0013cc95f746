import 'package:cloud_firestore/cloud_firestore.dart';

enum ReviewStatus { active, hidden, deleted }

extension ReviewStatusExtension on ReviewStatus {
  String get name {
    switch (this) {
      case ReviewStatus.active:
        return 'active';
      case ReviewStatus.hidden:
        return 'hidden';
      case ReviewStatus.deleted:
        return 'deleted';
    }
  }

  static ReviewStatus fromString(String? value) {
    switch (value) {
      case 'hidden':
        return ReviewStatus.hidden;
      case 'deleted':
        return ReviewStatus.deleted;
      default:
        return ReviewStatus.active;
    }
  }

  String get displayName {
    switch (this) {
      case ReviewStatus.active:
        return 'Hiển Thị';
      case ReviewStatus.hidden:
        return 'Đã Ẩn';
      case ReviewStatus.deleted:
        return 'Đã Xóa';
    }
  }
}

class ReviewModel {
  final String id;
  final String userId;
  final String userName;
  final String? userPhotoUrl;
  final int movieId;
  final double rating; // 1-5 sao
  final String? title;
  final String content;
  final bool isVerifiedPurchase; // đã mua vé hay chưa
  final String? ticketId; // nếu là verified purchase
  final int likes;
  final int dislikes;
  final List<String> likedBy; // userIds
  final List<String> dislikedBy; // userIds
  final bool isReported;
  final int reportCount;
  final ReviewStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  ReviewModel({
    required this.id,
    required this.userId,
    required this.userName,
    this.userPhotoUrl,
    required this.movieId,
    required this.rating,
    this.title,
    required this.content,
    this.isVerifiedPurchase = false,
    this.ticketId,
    this.likes = 0,
    this.dislikes = 0,
    this.likedBy = const [],
    this.dislikedBy = const [],
    this.isReported = false,
    this.reportCount = 0,
    this.status = ReviewStatus.active,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) {
    return ReviewModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      userName: json['userName'] ?? '',
      userPhotoUrl: json['userPhotoUrl'],
      movieId: json['movieId'] ?? 0,
      rating: json['rating']?.toDouble() ?? 0.0,
      title: json['title'],
      content: json['content'] ?? '',
      isVerifiedPurchase: json['isVerifiedPurchase'] ?? false,
      ticketId: json['ticketId'],
      likes: json['likes'] ?? 0,
      dislikes: json['dislikes'] ?? 0,
      likedBy: List<String>.from(json['likedBy'] ?? []),
      dislikedBy: List<String>.from(json['dislikedBy'] ?? []),
      isReported: json['isReported'] ?? false,
      reportCount: json['reportCount'] ?? 0,
      status: ReviewStatusExtension.fromString(json['status']),
      createdAt: json['createdAt'] != null 
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? (json['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  factory ReviewModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ReviewModel.fromJson({...data, 'id': doc.id});
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'userPhotoUrl': userPhotoUrl,
      'movieId': movieId,
      'rating': rating,
      'title': title,
      'content': content,
      'isVerifiedPurchase': isVerifiedPurchase,
      'ticketId': ticketId,
      'likes': likes,
      'dislikes': dislikes,
      'likedBy': likedBy,
      'dislikedBy': dislikedBy,
      'isReported': isReported,
      'reportCount': reportCount,
      'status': status.name,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Map<String, dynamic> toFirestore() => toJson();

  ReviewModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userPhotoUrl,
    int? movieId,
    double? rating,
    String? title,
    String? content,
    bool? isVerifiedPurchase,
    String? ticketId,
    int? likes,
    int? dislikes,
    List<String>? likedBy,
    List<String>? dislikedBy,
    bool? isReported,
    int? reportCount,
    ReviewStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userPhotoUrl: userPhotoUrl ?? this.userPhotoUrl,
      movieId: movieId ?? this.movieId,
      rating: rating ?? this.rating,
      title: title ?? this.title,
      content: content ?? this.content,
      isVerifiedPurchase: isVerifiedPurchase ?? this.isVerifiedPurchase,
      ticketId: ticketId ?? this.ticketId,
      likes: likes ?? this.likes,
      dislikes: dislikes ?? this.dislikes,
      likedBy: likedBy ?? this.likedBy,
      dislikedBy: dislikedBy ?? this.dislikedBy,
      isReported: isReported ?? this.isReported,
      reportCount: reportCount ?? this.reportCount,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get isActive => status == ReviewStatus.active;
  bool get isHidden => status == ReviewStatus.hidden;
  bool get isDeleted => status == ReviewStatus.deleted;

  bool get isVisible => status == ReviewStatus.active;
  bool get hasTitle => title != null && title!.isNotEmpty;
  bool get hasVerifiedPurchase => isVerifiedPurchase && ticketId != null;

  int get totalVotes => likes + dislikes;
  double get likeRatio => totalVotes > 0 ? likes / totalVotes : 0.0;

  bool isLikedBy(String userId) => likedBy.contains(userId);
  bool isDislikedBy(String userId) => dislikedBy.contains(userId);

  String get displayRating => rating.toStringAsFixed(1);
  String get displayStatus => status.displayName;

  String get ratingStars {
    final fullStars = rating.floor();
    final hasHalfStar = rating - fullStars >= 0.5;
    final emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    
    return '★' * fullStars + 
           (hasHalfStar ? '☆' : '') + 
           '☆' * emptyStars;
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} ngày trước';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} giờ trước';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} phút trước';
    } else {
      return 'Vừa xong';
    }
  }

  bool get canBeEdited => status == ReviewStatus.active;
  bool get canBeDeleted => status != ReviewStatus.deleted;
  bool get canBeReported => status == ReviewStatus.active && !isReported;

  // Validation methods
  bool get isValidRating => rating >= 1.0 && rating <= 5.0;
  bool get hasContent => content.isNotEmpty;
  bool get isValidReview => isValidRating && hasContent;

  // Content moderation
  bool get needsModeration => reportCount >= 3;
  bool get isHighlyRated => rating >= 4.0;
  bool get isLowRated => rating <= 2.0;
}
