import 'package:shared_preferences/shared_preferences.dart';

/// Utility class to help with storage cleanup
class StorageCleanup {
  /// Force clear all SharedPreferences data
  static Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    } catch (e) {
      // Ignore errors during cleanup
    }
  }

  /// Clear only user-related data
  static Future<void> clearUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Remove known user-related keys
      if (prefs.containsKey('user')) {
        await prefs.remove('user');
      }
      if (prefs.containsKey('isLoggedIn')) {
        await prefs.remove('isLoggedIn');
      }
    } catch (e) {
      // Ignore errors during cleanup
    }
  }

  /// Check if there's any problematic data and clear it
  static Future<void> checkAndFixProblematicData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Check for any keys that might be causing issues
      final allKeys = prefs.getKeys();
      for (final key in allKeys) {
        try {
          // Try to access the value - if it causes an error, remove the key
          prefs.get(key);
        } catch (e) {
          // If accessing the value causes an error, remove it
          await prefs.remove(key);
        }
      }
    } catch (e) {
      // If there's any error during the check, clear all data
      await clearAllData();
    }
  }
}
