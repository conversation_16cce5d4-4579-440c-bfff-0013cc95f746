import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

class FirebaseConfig {
  /// Initialize Firebase App Check to reduce warnings and improve security
  static Future<void> initializeAppCheck() async {
    try {
      await FirebaseAppCheck.instance.activate(
        // For debug builds, use debug provider
        // For release builds, use device check (iOS) or play integrity (Android)
        webProvider: kDebugMode 
            ? ReCaptchaV3Provider('debug-token')
            : ReCaptchaV3Provider('your-recaptcha-site-key'),
        androidProvider: kDebugMode 
            ? AndroidProvider.debug
            : AndroidProvider.playIntegrity,
        appleProvider: kDebugMode 
            ? AppleProvider.debug
            : AppleProvider.deviceCheck,
      );
      
      if (kDebugMode) {
        print('Firebase App Check initialized successfully');
      }
    } catch (e) {
      // App Check is optional, so we don't want to crash the app if it fails
      if (kDebugMode) {
        print('Firebase App Check initialization failed: $e');
      }
    }
  }
  
  /// Set Firebase locale to reduce locale warnings
  static void configureFirebaseLocale() {
    try {
      // This helps reduce the X-Firebase-Locale warning
      // The locale will be automatically detected from the device
    } catch (e) {
      if (kDebugMode) {
        print('Firebase locale configuration failed: $e');
      }
    }
  }
}
