[{"title": "Lilo & Stitch", "originalTitle": "Lilo & Stitch", "overview": "A lonely Hawaiian girl adopts a mischievous alien experiment posing as a dog.", "genres": "Family, Adventure, Comedy", "releaseDate": "2025-05-23", "runtime": 115, "voteAverage": 7.8, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/VWqJifMMgZE/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/VWqJifMMgZE/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/VWqJifMMgZE/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/VWqJifMMgZE/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=VWqJifMMgZE"}, {"title": "Mission: Impossible – The Final Reckoning", "originalTitle": "Mission: Impossible – The Final Reckoning", "overview": "<PERSON> faces his greatest threat yet in the explosive conclusion to the saga.", "genres": "Action, Thriller", "releaseDate": "2025-05-23", "runtime": 165, "voteAverage": 8.1, "ageRating": "PG-13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/fsQgc9pCyDU/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/fsQgc9pCyDU/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/fsQgc9pCyDU/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/fsQgc9pCyDU/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=fsQgc9pCyDU"}, {"title": "Thunderbolts*", "originalTitle": "Thunderbolts*", "overview": "A team of reformed super‑villains is sent on high‑risk missions by the U.S. government.", "genres": "Action, Adventure, Superhero", "releaseDate": "2025-05-02", "runtime": 139, "voteAverage": 7.4, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/-sAOWhvheK8/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/-sAOWhvheK8/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/-sAOWhvheK8/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/-sAOWhvheK8/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=-sAOWhvheK8"}, {"title": "Minecraft: The Movie", "originalTitle": "Minecraft", "overview": "An unexpected hero must save the Overworld from the Ender Dragon.", "genres": "Adventure, Fantasy", "releaseDate": "2025-04-04", "runtime": 109, "voteAverage": 7.2, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/wJO_vIDZn-I/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/wJO_vIDZn-I/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/wJO_vIDZn-I/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/wJO_vIDZn-I/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=wJO_vIDZn-I"}, {"title": "Final Destination: Bloodlines", "originalTitle": "Final Destination: Bloodlines", "overview": "A cursed family tries to break the death cycle after a premonition saves them.", "genres": "Horror, Thriller", "releaseDate": "2025-05-16", "runtime": 101, "voteAverage": 6.9, "ageRating": "R", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/UWMzKXsY9A4/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/UWMzKXsY9A4/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/UWMzKXsY9A4/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/UWMzKXsY9A4/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=UWMzKXsY9A4"}, {"title": "Captain America: Brave New World", "originalTitle": "Captain America: Brave New World", "overview": "<PERSON> grapples with global unrest as the new Captain <PERSON>.", "genres": "Action, Superhero", "releaseDate": "2025-02-14", "runtime": 148, "voteAverage": 7.5, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/1pHDWnXmK7Y/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/1pHDWnXmK7Y/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/1pHDWnXmK7Y/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/1pHDWnXmK7Y/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=1pHDWnXmK7Y"}, {"title": "Dog Man", "originalTitle": "Dog Man", "overview": "Half‑dog, half‑human, all hero—the beloved graphic‑novel character hits the big screen.", "genres": "Animation, Comedy, Family", "releaseDate": "2025-01-31", "runtime": 89, "voteAverage": 7.3, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/QaJbAennB_Q/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/QaJbAennB_Q/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/QaJbAennB_Q/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/QaJbAennB_Q/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=QaJbAennB_Q"}, {"title": "Paddington in Peru", "originalTitle": "Paddington in Peru", "overview": "<PERSON><PERSON> returns to Peru and embarks on a jungle adventure to find Aunt <PERSON>.", "genres": "Family, Adventure, Comedy", "releaseDate": "2025-02-14", "runtime": 107, "voteAverage": 8.0, "ageRating": "PG", "language": "English", "country": "UK", "director": "<PERSON><PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/NTvudSGfHRI/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/NTvudSGfHRI/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/NTvudSGfHRI/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/NTvudSGfHRI/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=NTvudSGfHRI"}, {"title": "Disney's <PERSON>", "originalTitle": "<PERSON>", "overview": "A modern live‑action re‑imagining of the 1937 classic.", "genres": "Fantasy, Musical, Adventure", "releaseDate": "2025-03-21", "runtime": 127, "voteAverage": 7.1, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/iV46TJKL8cU/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/iV46TJKL8cU/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/iV46TJKL8cU/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/iV46TJKL8cU/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=iV46TJKL8cU"}, {"title": "Heart Eyes", "originalTitle": "Heart Eyes", "overview": "A Valentine's Day serial‑killer fixes on a couple who aren't what they seem.", "genres": "Horror, Romance", "releaseDate": "2025-02-07", "runtime": 97, "voteAverage": 6.5, "ageRating": "R", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/1cRzZcMlJh8/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/1cRzZcMlJh8/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/1cRzZcMlJh8/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/1cRzZcMlJh8/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=1cRzZcMlJh8"}, {"title": "Love Hurts", "originalTitle": "Love Hurts", "overview": "A retired assassin realtor must confront his violent past.", "genres": "Action, Comedy", "releaseDate": "2025-02-07", "runtime": 83, "voteAverage": 6.0, "ageRating": "R", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/frYVyUDIwiE/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/frYVyUDIwiE/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/frYVyUDIwiE/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/frYVyUDIwiE/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=frYVyUDIwiE"}, {"title": "The Monkey", "originalTitle": "The Monkey", "overview": "A cursed cymbal‑banging toy unleashes carnage on twin brothers.", "genres": "Horror, Thriller", "releaseDate": "2025-02-21", "runtime": 98, "voteAverage": 7.0, "ageRating": "R", "language": "English", "country": "USA", "director": "<PERSON><PERSON><PERSON> Perkins", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/husMGbXEIho/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/husMGbXEIho/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/husMGbXEIho/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/husMGbXEIho/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=husMGbXEIho"}, {"title": "Last Breath", "originalTitle": "Last Breath", "overview": "Deep‑sea divers fight to save a trapped teammate hundreds of feet below.", "genres": "Thriller, Drama", "releaseDate": "2025-02-28", "runtime": 93, "voteAverage": 7.2, "ageRating": "PG‑13", "language": "English", "country": "UK", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/sNMyooXZZTM/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/sNMyooXZZTM/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/sNMyooXZZTM/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/sNMyooXZZTM/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=sNMyooXZZTM"}, {"title": "The Unbreakable Boy", "originalTitle": "The Unbreakable Boy", "overview": "Based on the true story of a child whose optimism inspires everyone.", "genres": "Drama, Biography", "releaseDate": "2025-02-21", "runtime": 109, "voteAverage": 6.7, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/pGbLX3__m60/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/pGbLX3__m60/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/pGbLX3__m60/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/pGbLX3__m60/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=pGbLX3__m60"}, {"title": "Companion", "originalTitle": "Companion", "overview": "A weekend getaway unravels when a guest discovers she's an AI companion.", "genres": "<PERSON><PERSON><PERSON><PERSON>, Thriller", "releaseDate": "2025-01-31", "runtime": 97, "voteAverage": 7.1, "ageRating": "R", "language": "English", "country": "USA", "director": "<PERSON>", "status": "now_playing", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/Qr_kX0D3DNA/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/Qr_kX0D3DNA/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/Qr_kX0D3DNA/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/Qr_kX0D3DNA/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=Qr_kX0D3DNA"}, {"title": "Jurassic World: Rebirth", "originalTitle": "Jurassic World: Rebirth", "overview": "A covert team returns to Isla Sorna to recover dangerous dinosaur DNA.", "genres": "Action, Sci‑Fi, Adventure", "releaseDate": "2025-07-02", "runtime": 145, "voteAverage": 0, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/jan5CFWs9ic/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/jan5CFWs9ic/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/jan5CFWs9ic/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/jan5CFWs9ic/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=jan5CFWs9ic"}, {"title": "How to Train Your Dragon", "originalTitle": "How to Train Your Dragon", "overview": "Live‑action re‑telling of <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>'s first adventure.", "genres": "Fantasy, Adventure", "releaseDate": "2025-06-13", "runtime": 132, "voteAverage": 0, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/OWEq2Pf8qpk/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/OWEq2Pf8qpk/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/OWEq2Pf8qpk/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/OWEq2Pf8qpk/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=OWEq2Pf8qpk"}, {"title": "Smurfs", "originalTitle": "Smurfs", "overview": "The Smurfs leave their village to save <PERSON>murf from dark magic.", "genres": "Animation, Family, Fantasy", "releaseDate": "2025-07-18", "runtime": 95, "voteAverage": 0, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/108UjvIzE64/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/108UjvIzE64/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/108UjvIzE64/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/108UjvIzE64/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=108UjvIzE64"}, {"title": "Shrek 5", "originalTitle": "Shrek 5", "overview": "An older <PERSON><PERSON><PERSON> must rescue his teenage daughter from a new villain.", "genres": "Animation, Comedy, Fantasy", "releaseDate": "2026-12-23", "runtime": 102, "voteAverage": 0, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/I9-wXs4KtrU/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/I9-wXs4KtrU/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/I9-wXs4KtrU/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/I9-wXs4KtrU/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=I9-wXs4KtrU"}, {"title": "Weapons", "originalTitle": "Weapons", "overview": "A small town unravels after 17 children vanish at 2:17 a.m.", "genres": "Horror, Mystery", "releaseDate": "2025-08-08", "runtime": 128, "voteAverage": 0, "ageRating": "R", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/OpThntO9ixc/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/OpThntO9ixc/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/OpThntO9ixc/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/OpThntO9ixc/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=OpThntO9ixc"}, {"title": "Dune: Messiah", "originalTitle": "Dune: Messiah", "overview": "<PERSON> faces political intrigue and prophecy on <PERSON><PERSON><PERSON>.", "genres": "Sci‑Fi, Adventure, Drama", "releaseDate": "2026-12-18", "runtime": 170, "voteAverage": 0, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/ELl58ocNEd0/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/ELl58ocNEd0/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/ELl58ocNEd0/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/ELl58ocNEd0/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=ELl58ocNEd0"}, {"title": "Avengers: Doomsday", "originalTitle": "Avengers: Doomsday", "overview": "Earth's mightiest heroes assemble to battle <PERSON>.", "genres": "Action, Superhero", "releaseDate": "2026-12-18", "runtime": 182, "voteAverage": 0, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>, <PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/9kMKobAx6bk/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/9kMKobAx6bk/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/9kMKobAx6bk/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/9kMKobAx6bk/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=9kMKobAx6bk"}, {"title": "The Odyssey", "originalTitle": "The Odyssey", "overview": "<PERSON> adapts <PERSON>'s epic journey of Odysseus.", "genres": "Action, Fantasy, Adventure", "releaseDate": "2026-07-17", "runtime": 195, "voteAverage": 0, "ageRating": "PG‑13", "language": "English", "country": "UK/USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/YUVDqQXi1mg/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/YUVDqQXi1mg/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/YUVDqQXi1mg/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/YUVDqQXi1mg/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=YUVDqQXi1mg"}, {"title": "The Mandalorian and Grogu", "originalTitle": "The Mandalorian and Grogu", "overview": "<PERSON> and <PERSON><PERSON><PERSON> embark on a galaxy‑spanning quest.", "genres": "Sci‑Fi, Adventure", "releaseDate": "2026-05-22", "runtime": 138, "voteAverage": 0, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/iAzXfo-tfK0/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/iAzXfo-tfK0/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/iAzXfo-tfK0/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/iAzXfo-tfK0/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=iAzXfo-tfK0"}, {"title": "Supergirl: Woman of Tomorrow", "originalTitle": "Supergirl: Woman of Tomorrow", "overview": "<PERSON> Zor‑El seeks justice across the cosmos with Ruthye and Krypto.", "genres": "Action, Sci‑Fi, Superhero", "releaseDate": "2026-06-26", "runtime": 149, "voteAverage": 0, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/Hp8HyB04Peg/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/Hp8HyB04Peg/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/Hp8HyB04Peg/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/Hp8HyB04Peg/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=Hp8HyB04Peg"}, {"title": "The Devil Wears Prada 2", "originalTitle": "The Devil Wears Prada 2", "overview": "<PERSON> battles digital disruption in high fashion.", "genres": "Drama, Comedy", "releaseDate": "2026-05-01", "runtime": 112, "voteAverage": 0, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/XHq17gmQGjI/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/XHq17gmQGjI/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/XHq17gmQGjI/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/XHq17gmQGjI/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=XHq17gmQGjI"}, {"title": "Spider‑Man: <PERSON> New Day", "originalTitle": "Spider‑Man 4: <PERSON> New Day", "overview": "<PERSON> faces new foes as college life collides with heroism.", "genres": "Action, Superhero", "releaseDate": "2026-07-31", "runtime": 151, "voteAverage": 0, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON><PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/7r93JmbVFo8/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/7r93JmbVFo8/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/7r93JmbVFo8/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/7r93JmbVFo8/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=7r93JmbVFo8"}, {"title": "Project Hail Mary", "originalTitle": "Project Hail Mary", "overview": "A lone astronaut must save Earth from an extinction‑level threat.", "genres": "Sci‑Fi, Adventure", "releaseDate": "2026-03-20", "runtime": 158, "voteAverage": 0, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>, <PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/3VQ9OJ_v81E/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/3VQ9OJ_v81E/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/3VQ9OJ_v81E/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/3VQ9OJ_v81E/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=3VQ9OJ_v81E"}, {"title": "<PERSON><PERSON> (Live‑Action)", "originalTitle": "<PERSON><PERSON>", "overview": "<PERSON><PERSON> sets sail once more in a live‑action adaptation.", "genres": "Adventure, Musical, Family", "releaseDate": "2026-07-10", "runtime": 137, "voteAverage": 0, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/0Vh9c3rYSRU/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/0Vh9c3rYSRU/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/0Vh9c3rYSRU/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/0Vh9c3rYSRU/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=0Vh9c3rYSRU"}, {"title": "Shazam! Fury of the Gods (Deluxe Re‑release)", "originalTitle": "Shazam! Fury of the Gods", "overview": "4K IMAX remaster ahead of sequel release.", "genres": "Action, Superhero", "releaseDate": "2025-12-05", "runtime": 131, "voteAverage": 0, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "upcoming", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/KRdX4KY-f4Q/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/KRdX4KY-f4Q/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/KRdX4KY-f4Q/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/KRdX4KY-f4Q/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=KRdX4KY-f4Q"}, {"title": "Inside Out 2", "originalTitle": "Inside Out 2", "overview": "<PERSON>'s teenage years introduce brand‑new emotions to her mind HQ.", "genres": "Animation, Family, Comedy", "releaseDate": "2024-06-14", "runtime": 100, "voteAverage": 8.2, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/LEjhY15eCx0/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/LEjhY15eCx0/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/LEjhY15eCx0/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/LEjhY15eCx0/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=LEjhY15eCx0"}, {"title": "<PERSON><PERSON><PERSON>", "originalTitle": "<PERSON><PERSON><PERSON>", "overview": "The story of <PERSON><PERSON> and the development of the atomic bomb.", "genres": "Drama, History", "releaseDate": "2023-07-21", "runtime": 180, "voteAverage": 8.5, "ageRating": "R", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/uYPbbksJxIg/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/uYPbbksJxIg/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/uYPbbksJxIg/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/uYPbbksJxIg/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=uYPbbksJxIg"}, {"title": "Barbie", "originalTitle": "Barbie", "overview": "<PERSON> leaves Barbieland to discover the real world and herself.", "genres": "Comedy, Fantasy", "releaseDate": "2023-07-21", "runtime": 114, "voteAverage": 7.4, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON><PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/pBk4NYhWNMM/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/pBk4NYhWNMM/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/pBk4NYhWNMM/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/pBk4NYhWNMM/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=pBk4NYhWNMM"}, {"title": "Dune: Part Two", "originalTitle": "Dune: Part Two", "overview": "<PERSON> and <PERSON><PERSON> unite Fremen tribes to wage war against House Harkonnen.", "genres": "Sci‑Fi, Adventure, Drama", "releaseDate": "2024-03-01", "runtime": 166, "voteAverage": 8.4, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/Way9Dexny3w/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/Way9Dexny3w/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/Way9Dexny3w/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/Way9Dexny3w/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=Way9Dexny3w"}, {"title": "Godzilla x Kong: The New Empire", "originalTitle": "Godzilla x Kong: The New Empire", "overview": "Titans unite against a hidden colossal threat beneath Hollow Earth.", "genres": "Action, Sci‑Fi", "releaseDate": "2024-04-12", "runtime": 115, "voteAverage": 7.1, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/qqrpMRDuPfc/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/qqrpMRDuPfc/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/qqrpMRDuPfc/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/qqrpMRDuPfc/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=qqrpMRDuPfc"}, {"title": "Deadpool & Wolverine", "originalTitle": "Deadpool & Wolverine", "overview": "<PERSON><PERSON> joins forces with <PERSON> in an anarchic multiverse caper.", "genres": "Action, Comedy, Superhero", "releaseDate": "2024-07-26", "runtime": 128, "voteAverage": 8.0, "ageRating": "R", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/4uXPCGFxDHM/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/4uXPCGFxDHM/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/4uXPCGFxDHM/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/4uXPCGFxDHM/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=4uXPCGFxDHM"}, {"title": "Guardians of the Galaxy Vol. 3", "originalTitle": "Guardians of the Galaxy Vol. 3", "overview": "The Guardians attempt to save <PERSON>'s life while confronting their past.", "genres": "Action, Sci‑Fi, Comedy", "releaseDate": "2023-05-05", "runtime": 150, "voteAverage": 8.1, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/u3V5KDHRQvk/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/u3V5KDHRQvk/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/u3V5KDHRQvk/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/u3V5KDHRQvk/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=u3V5KDHRQvk"}, {"title": "The Super Mario Bros. Movie", "originalTitle": "The Super Mario Bros. Movie", "overview": "<PERSON> and <PERSON> team up to stop <PERSON><PERSON>'s plans in the Mushroom Kingdom.", "genres": "Animation, Adventure", "releaseDate": "2023-04-05", "runtime": 92, "voteAverage": 7.2, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>, <PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/TnGl01FkMMo/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/TnGl01FkMMo/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/TnGl01FkMMo/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/TnGl01FkMMo/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=TnGl01FkMMo"}, {"title": "Wonka", "originalTitle": "Wonka", "overview": "The origin story of <PERSON> and his chocolate empire.", "genres": "Family, Fantasy", "releaseDate": "2023-12-15", "runtime": 116, "voteAverage": 7.3, "ageRating": "PG", "language": "English", "country": "UK/USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/otNh9bTjXWg/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/otNh9bTjXWg/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/otNh9bTjXWg/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/otNh9bTjXWg/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=otNh9bTjXWg"}, {"title": "The Little Mermaid", "originalTitle": "The Little Mermaid", "overview": "A young mermaid makes a deal with a sea witch to see the surface.", "genres": "Fantasy, Romance", "releaseDate": "2023-05-26", "runtime": 135, "voteAverage": 7.0, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/kpGo2_d3oYE/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/kpGo2_d3oYE/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/kpGo2_d3oYE/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/kpGo2_d3oYE/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=kpGo2_d3oYE"}, {"title": "Spider‑Man: Across the Spider‑Verse", "originalTitle": "Spider‑Man: Across the Spider‑Verse", "overview": "<PERSON> travels across the Multiverse to find allies against a new foe.", "genres": "Animation, Action", "releaseDate": "2023-06-02", "runtime": 140, "voteAverage": 8.9, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/cqGjhVJWtEg/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/cqGjhVJWtEg/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/cqGjhVJWtEg/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/cqGjhVJWtEg/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=cqGjhVJWtEg"}, {"title": "Mission: Impossible – Dead Reckoning Part One", "originalTitle": "Mission: Impossible – Dead Reckoning Part One", "overview": "<PERSON> races to control a weapons‑grade AI.", "genres": "Action, Thriller", "releaseDate": "2023-07-12", "runtime": 163, "voteAverage": 8.0, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/avz06PDqDbM/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/avz06PDqDbM/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/avz06PDqDbM/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/avz06PDqDbM/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=avz06PDqDbM"}, {"title": "The Flash", "originalTitle": "The Flash", "overview": "<PERSON> travels back in time, altering the multiverse.", "genres": "Superhero, Sci‑Fi", "releaseDate": "2023-06-16", "runtime": 144, "voteAverage": 6.3, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/jprhe-cWKGs/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/jprhe-cWKGs/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/jprhe-cWKGs/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/jprhe-cWKGs/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=jprhe-cWKGs"}, {"title": "Elemental", "originalTitle": "Elemental", "overview": "Opposites react when fire element Ember meets water element <PERSON>.", "genres": "Animation, Romance, Comedy", "releaseDate": "2023-06-16", "runtime": 101, "voteAverage": 7.1, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/hXzcyx9V0xw/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/hXzcyx9V0xw/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/hXzcyx9V0xw/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/hXzcyx9V0xw/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=hXzcyx9V0xw"}, {"title": "The Batman", "originalTitle": "The Batman", "overview": "<PERSON> uncovers corruption in Gotham connected to his own family.", "genres": "Crime, Thriller, <PERSON>hero", "releaseDate": "2022-03-04", "runtime": 176, "voteAverage": 8.1, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/mqqft2x_Aa4/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/mqqft2x_Aa4/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/mqqft2x_Aa4/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/mqqft2x_Aa4/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=mqqft2x_Aa4"}, {"title": "Doctor <PERSON> in the Multiverse of Madness", "originalTitle": "Doctor <PERSON> in the Multiverse of Madness", "overview": "Strange traverses mind‑bending realities to protect <PERSON> Chavez.", "genres": "Action, Fantasy", "releaseDate": "2022-05-06", "runtime": 126, "voteAverage": 7.0, "ageRating": "PG‑13", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/aWzlQ2N6qqg/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/aWzlQ2N6qqg/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/aWzlQ2N6qqg/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/aWzlQ2N6qqg/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=aWzlQ2N6qqg"}, {"title": "Sonic the Hedgehog 2", "originalTitle": "Sonic the Hedgehog 2", "overview": "<PERSON> and <PERSON><PERSON> race to stop <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>.", "genres": "Family, Adventure", "releaseDate": "2022-04-08", "runtime": 122, "voteAverage": 7.3, "ageRating": "PG", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/G5kzUpWAusI/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/G5kzUpWAusI/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/G5kzUpWAusI/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/G5kzUpWAusI/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=G5kzUpWAusI"}, {"title": "No Time to Die", "originalTitle": "No Time to Die", "overview": "<PERSON> comes out of retirement to face <PERSON><PERSON>'s bioweapon plot.", "genres": "Action, Thriller", "releaseDate": "2021-10-08", "runtime": 163, "voteAverage": 7.4, "ageRating": "PG‑13", "language": "English", "country": "UK/USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/BIhNsAtPbPI/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/BIhNsAtPbPI/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/BIhNsAtPbPI/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/BIhNsAtPbPI/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=BIhNsAtPbPI"}, {"title": "Tenet", "originalTitle": "Tenet", "overview": "A secret agent manipulates time to prevent World War III.", "genres": "Sci‑Fi, Action", "releaseDate": "2020-08-26", "runtime": 150, "voteAverage": 7.4, "ageRating": "PG‑13", "language": "English", "country": "UK/USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/LdOM0x0XDMo/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/LdOM0x0XDMo/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/LdOM0x0XDMo/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/LdOM0x0XDMo/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=LdOM0x0XDMo"}, {"title": "Joker", "originalTitle": "Joker", "overview": "<PERSON>'s descent into madness births Gotham's infamous clown.", "genres": "Crime, Drama", "releaseDate": "2019-10-04", "runtime": 122, "voteAverage": 8.4, "ageRating": "R", "language": "English", "country": "USA", "director": "<PERSON>", "status": "ended", "isActive": true, "isHomeBanner": false, "isSplashBanner": false, "posterPath": "https://img.youtube.com/vi/zAGVQLHvwOY/hqdefault.jpg", "poster_path": "https://img.youtube.com/vi/zAGVQLHvwOY/hqdefault.jpg", "backdropPath": "https://img.youtube.com/vi/zAGVQLHvwOY/maxresdefault.jpg", "backdrop_path": "https://img.youtube.com/vi/zAGVQLHvwOY/maxresdefault.jpg", "trailerUrl": "https://www.youtube.com/watch?v=zAGVQLHvwOY"}]