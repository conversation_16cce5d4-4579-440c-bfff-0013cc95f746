import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';

class StorageService {
  static const String _userKey = 'user';
  static const String _isLoggedInKey = 'isLoggedIn';

  Future<void> saveUser(UserModel user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, jsonEncode(user.toJson()));
  }

  Future<UserModel?> getUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);
      if (userJson != null) {
        // Try to decode the JSON and create a UserModel
        final decodedJson = jsonDecode(userJson);
        if (decodedJson is Map<String, dynamic>) {
          return UserModel.fromJson(decodedJson);
        } else {
          // If the decoded JSON is not a Map<String, dynamic>, clear it and return null
          await prefs.remove(_userKey);
          return null;
        }
      }
      return null;
    } catch (e) {
      // If there's any error (like type casting), clear the stored user data
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
      return null;
    }
  }

  Future<void> setLoggedIn(bool isLoggedIn) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isLoggedInKey, isLoggedIn);
  }

  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  /// Clears only user-related data
  Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
    await prefs.remove(_isLoggedInKey);
  }
}
