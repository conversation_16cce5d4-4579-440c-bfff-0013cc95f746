import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/ticket_model.dart';
import '../controllers/auth_controller.dart';

class TicketController extends GetxController {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final RxList<Ticket> tickets = <Ticket>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  late AuthController _authController;

  @override
  void onInit() {
    super.onInit();
    _authController = Get.find<AuthController>();
    loadTickets();

    // Listen to auth changes to reload tickets when user logs in/out
    ever(_authController.isLoggedInObs, (_) {
      loadTickets();
    });
  }

  Future<void> loadTickets() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      tickets.clear();

      if (_authController.isLoggedIn) {
        // User is logged in, try to load from Firestore
        await _loadFromFirestore();
      } else {
        // User is not logged in, load from local storage
        await _loadFromLocalStorage();
      }
    } catch (e) {
      errorMessage.value = 'Failed to load tickets: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _loadFromFirestore() async {
    try {
      final userId = _authController.user?.id;
      if (userId == null) return;

      final snapshot = await _firestore
          .collection('users')
          .doc(userId)
          .collection('tickets')
          .orderBy('purchase_date', descending: true)
          .get();

      final List<Ticket> loadedTickets = [];
      for (var doc in snapshot.docs) {
        final data = doc.data();
        data['id'] = doc.id; // Ensure the document ID is included
        loadedTickets.add(Ticket.fromJson(data));
      }

      tickets.value = loadedTickets;
    } catch (e) {
      // If Firestore fails, try to load from local storage as fallback
      await _loadFromLocalStorage();
    }
  }

  Future<void> _loadFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ticketsJson = prefs.getStringList('tickets') ?? [];

      final List<Ticket> loadedTickets = [];
      for (var json in ticketsJson) {
        final Map<String, dynamic> data = jsonDecode(json);
        
        // Convert string date to Timestamp for local storage
        if (data['purchase_date'] is String) {
          data['purchase_date'] = Timestamp.fromDate(
            DateTime.parse(data['purchase_date'])
          );
        }
        
        loadedTickets.add(Ticket.fromJson(data));
      }

      // Sort by purchase date (newest first)
      loadedTickets.sort((a, b) => b.purchaseDate.compareTo(a.purchaseDate));
      tickets.value = loadedTickets;
    } catch (e) {
      errorMessage.value = 'Failed to load tickets from local storage: $e';
    }
  }

  Future<bool> purchaseTicket(Ticket ticket) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // Add to tickets list
      tickets.add(ticket);

      // Save changes
      if (_authController.isLoggedIn) {
        await _saveToFirestore(ticket);
      }
      await _saveToLocalStorage();
      
      return true;
    } catch (e) {
      errorMessage.value = 'Failed to purchase ticket: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _saveToFirestore(Ticket ticket) async {
    try {
      final userId = _authController.user?.id;
      if (userId == null) return;

      // Add ticket to Firestore
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('tickets')
          .doc(ticket.id)
          .set(ticket.toJson());
    } catch (e) {
      // If Firestore fails, at least save to local storage
      errorMessage.value = 'Failed to save ticket to Firestore: $e';
    }
  }

  Future<void> _saveToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final List<String> ticketsJson = tickets.map((ticket) {
        final Map<String, dynamic> data = ticket.toJson();
        
        // Convert Timestamp to string for local storage
        if (data['purchase_date'] is Timestamp) {
          data['purchase_date'] = (data['purchase_date'] as Timestamp)
              .toDate()
              .toIso8601String();
        }
        
        return jsonEncode(data);
      }).toList();
      
      await prefs.setStringList('tickets', ticketsJson);
    } catch (e) {
      errorMessage.value = 'Failed to save tickets to local storage: $e';
    }
  }
}
