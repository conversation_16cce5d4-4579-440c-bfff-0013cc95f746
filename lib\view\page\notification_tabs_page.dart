import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../page/new_notification_page.dart';
import '../../controllers/notification_controller.dart';

class NotificationTabsPage extends StatefulWidget {
  const NotificationTabsPage({Key? key}) : super(key: key);

  @override
  State<NotificationTabsPage> createState() => _NotificationTabsPageState();
}

class _NotificationTabsPageState extends State<NotificationTabsPage> {
  // Đã xóa SingleTickerProviderStateMixin và TabController vì không cần nữa

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Thông báo',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xff4B79A1), Color(0xff283E51)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Làm mới thông báo
              final controller = Get.find<NotificationController>();
              controller.fetchNotifications();
              Get.snackbar(
                'Làm mới',
                'Đang làm mới thông báo...',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
          ),
        ],
      ),
      body: const SafeArea(
        child: NewNotificationPage(),
      ),
    );
  }
}
