import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'models/banner_model.dart';
import 'debug_add_banner.dart';
import 'utils/developer_mode.dart';

class FirestoreDebugPage extends StatefulWidget {
  const FirestoreDebugPage({Key? key}) : super(key: key);

  @override
  State<FirestoreDebugPage> createState() => _FirestoreDebugPageState();
}

class _FirestoreDebugPageState extends State<FirestoreDebugPage> {
  bool _isLoading = false;
  String _errorMessage = '';
  List<Map<String, dynamic>> _banners = [];
  List<BannerModel> _bannerModels = [];

  @override
  void initState() {
    super.initState();
    _checkDeveloperAccess();
  }

  void _checkDeveloperAccess() {
    final developerMode = Get.find<DeveloperMode>();
    if (!developerMode.canPerformDebugAction()) {
      // Không có quyền truy cập, quay lại trang trước
      Get.snackbar(
        '<PERSON>hông có quyền truy cập',
        '<PERSON>ạ<PERSON> không có quyền truy cập trang debug này.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      Future.delayed(const Duration(milliseconds: 500), () {
        Get.back();
      });
      return;
    }

    // Có quyền truy cập, tiếp tục tải dữ liệu
    _testFirestoreConnection();
  }

  Future<void> _testFirestoreConnection() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _banners = [];
      _bannerModels = [];
    });

    try {
      // Test basic connection
      final firestore = FirebaseFirestore.instance;

      // Get all documents from 'banners' collection
      final snapshot = await firestore.collection('banners').get();

      if (snapshot.docs.isEmpty) {
        setState(() {
          _errorMessage = "No documents found in 'banners' collection";
          _isLoading = false;
        });
        return;
      }

      // Get raw data
      final banners = snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id; // Add document ID to data
        return data;
      }).toList();

      // Try to convert to BannerModel
      final bannerModels = snapshot.docs
          .map((doc) {
            try {
              return BannerModel.fromFirestore(doc);
            } catch (e) {
              _errorMessage += "\nError parsing document ${doc.id}: $e";
              return null;
            }
          })
          .where((model) => model != null)
          .cast<BannerModel>()
          .toList();

      setState(() {
        _banners = banners;
        _bannerModels = bannerModels;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = "Error connecting to Firestore: $e";
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firestore Debug'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Add Test Banner',
            onPressed: () {
              Get.to(() => const AddBannerDebugPage());
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _testFirestoreConnection,
          ),
        ],
      ),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_errorMessage.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _errorMessage,
                          style: const TextStyle(
                            color: Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    Text(
                      'Raw Banners (${_banners.length}):',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (_banners.isEmpty)
                      const Text('No banners found in Firestore')
                    else
                      ...List.generate(_banners.length, (index) {
                        final banner = _banners[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 16),
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Document ID: ${banner['id']}',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold),
                                ),
                                const Divider(),
                                ...banner.entries
                                    .where((e) => e.key != 'id')
                                    .map((e) => Padding(
                                          padding:
                                              const EdgeInsets.only(bottom: 4),
                                          child: Text('${e.key}: ${e.value}'),
                                        )),
                                if (banner['imageUrl'] != null)
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Divider(),
                                      const Text(
                                        'Image Preview:',
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold),
                                      ),
                                      const SizedBox(height: 8),
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(8),
                                        child: Image.network(
                                          banner['imageUrl'],
                                          height: 150,
                                          width: double.infinity,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) {
                                            return Container(
                                              height: 150,
                                              width: double.infinity,
                                              color: Colors.grey[300],
                                              child: Center(
                                                child: Text(
                                                  'Error loading image: $error',
                                                  textAlign: TextAlign.center,
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                              ],
                            ),
                          ),
                        );
                      }),
                    const SizedBox(height: 24),
                    Text(
                      'Parsed Banner Models (${_bannerModels.length}):',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (_bannerModels.isEmpty)
                      const Text('No banner models could be parsed')
                    else
                      ...List.generate(_bannerModels.length, (index) {
                        final banner = _bannerModels[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 16),
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'ID: ${banner.id}',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold),
                                ),
                                Text('Title: ${banner.title}'),
                                Text('Type: ${banner.type}'),
                                Text('Active: ${banner.isActive}'),
                                Text('Order: ${banner.order}'),
                                if (banner.description != null)
                                  Text('Description: ${banner.description}'),
                                const Divider(),
                                const Text(
                                  'Image Preview:',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(height: 8),
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    banner.imageUrl,
                                    height: 150,
                                    width: double.infinity,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        height: 150,
                                        width: double.infinity,
                                        color: Colors.grey[300],
                                        child: Center(
                                          child: Text(
                                            'Error loading image: $error',
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }),
                  ],
                ),
              ),
      ),
    );
  }
}
