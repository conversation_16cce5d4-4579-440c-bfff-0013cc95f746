import 'dart:developer' as developer;
import 'package:get/get.dart';
import '../models/banner_model.dart';
import '../services/banner_service.dart';

class BannerController extends GetxController {
  // Log helper
  void _log(String message) {
    developer.log(message, name: 'BannerController');
  }

  final BannerService _bannerService = BannerService();

  // Observable lists for different banner types
  final RxList<BannerModel> allBanners = <BannerModel>[].obs;
  final RxList<BannerModel> homeBanners = <BannerModel>[].obs;
  final RxList<BannerModel> splashBanners = <BannerModel>[].obs;

  // Selected banner for editing
  final Rx<BannerModel?> selectedBanner = Rx<BannerModel?>(null);

  // Loading states
  final RxBool isLoading = false.obs;
  final RxBool isSubmitting = false.obs;

  // Error message
  final RxString errorMessage = ''.obs;

  // Filter for admin view
  final Rx<BannerType?> currentFilter = Rx<BannerType?>(null);

  @override
  void onInit() {
    super.onInit();
    _log('BannerController initialized');
    fetchAllBanners();
  }

  // Fetch all banners
  Future<void> fetchAllBanners() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final banners = await _bannerService.getBanners();
      allBanners.value = banners;

      // Filter banners by type
      _filterBannersByType();
    } catch (e) {
      errorMessage.value = 'Failed to load banners: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // Fetch active banners for the app
  Future<void> fetchActiveBanners({BannerType? type}) async {
    try {
      _log('Fetching active banners, type: ${type?.toString() ?? 'all'}');
      isLoading.value = true;
      errorMessage.value = '';

      // Add a fallback mechanism to fetch all banners if specific type query fails
      if (type == BannerType.home) {
        _log('Fetching home banners');
        try {
          final banners =
              await _bannerService.getActiveBanners(type: BannerType.home);
          _log('Received ${banners.length} home banners');
          homeBanners.value = banners;

          // If no banners found, try fetching without type filter as fallback
          if (banners.isEmpty) {
            _log(
                'No home banners found, trying to fetch all banners as fallback');
            final allBanners = await _bannerService.getActiveBanners();
            _log('Received ${allBanners.length} total banners');

            // Filter home banners client-side
            final filteredHomeBanners = allBanners
                .where((banner) => banner.type == BannerType.home)
                .toList();

            _log(
                'Filtered ${filteredHomeBanners.length} home banners client-side');
            if (filteredHomeBanners.isNotEmpty) {
              homeBanners.value = filteredHomeBanners;
            }
          }
        } catch (e) {
          _log('Error fetching home banners: $e, trying fallback');
          // Try a simpler query as fallback
          final allBanners = await _bannerService.getBanners();
          final filteredHomeBanners = allBanners
              .where(
                  (banner) => banner.type == BannerType.home && banner.isActive)
              .toList();

          _log('Fallback: filtered ${filteredHomeBanners.length} home banners');
          homeBanners.value = filteredHomeBanners;
        }
      } else if (type == BannerType.splash) {
        _log('Fetching splash banners');
        try {
          final banners =
              await _bannerService.getActiveBanners(type: BannerType.splash);
          _log('Received ${banners.length} splash banners');
          splashBanners.value = banners;

          // If no banners found, try fetching without type filter as fallback
          if (banners.isEmpty) {
            _log(
                'No splash banners found, trying to fetch all banners as fallback');
            final allBanners = await _bannerService.getActiveBanners();

            // Filter splash banners client-side
            final filteredSplashBanners = allBanners
                .where((banner) => banner.type == BannerType.splash)
                .toList();

            _log(
                'Filtered ${filteredSplashBanners.length} splash banners client-side');
            if (filteredSplashBanners.isNotEmpty) {
              splashBanners.value = filteredSplashBanners;
            }
          }
        } catch (e) {
          _log('Error fetching splash banners: $e, trying fallback');
          // Try a simpler query as fallback
          final allBanners = await _bannerService.getBanners();
          final filteredSplashBanners = allBanners
              .where((banner) =>
                  banner.type == BannerType.splash && banner.isActive)
              .toList();

          _log(
              'Fallback: filtered ${filteredSplashBanners.length} splash banners');
          splashBanners.value = filteredSplashBanners;
        }
      } else {
        // Fetch both types
        _log('Fetching both home and splash banners');
        try {
          final allBanners = await _bannerService.getActiveBanners();
          _log('Received ${allBanners.length} total banners');

          // Filter by type client-side
          final filteredHomeBanners = allBanners
              .where((banner) => banner.type == BannerType.home)
              .toList();
          final filteredSplashBanners = allBanners
              .where((banner) => banner.type == BannerType.splash)
              .toList();

          _log(
              'Filtered ${filteredHomeBanners.length} home banners and ${filteredSplashBanners.length} splash banners');
          homeBanners.value = filteredHomeBanners;
          splashBanners.value = filteredSplashBanners;
        } catch (e) {
          _log('Error fetching all banners: $e, trying fallback');
          // Try a simpler query as fallback
          final allBanners = await _bannerService.getBanners();

          final filteredHomeBanners = allBanners
              .where(
                  (banner) => banner.type == BannerType.home && banner.isActive)
              .toList();
          final filteredSplashBanners = allBanners
              .where((banner) =>
                  banner.type == BannerType.splash && banner.isActive)
              .toList();

          _log(
              'Fallback: filtered ${filteredHomeBanners.length} home banners and ${filteredSplashBanners.length} splash banners');
          homeBanners.value = filteredHomeBanners;
          splashBanners.value = filteredSplashBanners;
        }
      }
    } catch (e) {
      _log('Error fetching active banners: $e');
      errorMessage.value = 'Failed to load active banners: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // Filter banners by type
  void _filterBannersByType() {
    final BannerType? filter = currentFilter.value;

    if (filter == null) {
      // No filter, show all banners
      return;
    }

    final filtered =
        allBanners.where((banner) => banner.type == filter).toList();

    if (filter == BannerType.home) {
      homeBanners.value = filtered;
    } else if (filter == BannerType.splash) {
      splashBanners.value = filtered;
    }
  }

  // Set filter for admin view
  void setFilter(BannerType? type) {
    currentFilter.value = type;
    _filterBannersByType();
  }

  // Get a banner by ID
  Future<BannerModel?> getBannerById(String id) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final banner = await _bannerService.getBannerById(id);
      selectedBanner.value = banner;

      return banner;
    } catch (e) {
      errorMessage.value = 'Failed to load banner: $e';
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // Add a new banner
  Future<bool> addBanner(BannerModel banner) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      final newBanner = await _bannerService.addBanner(banner);

      // Add to the appropriate list
      allBanners.add(newBanner);

      if (banner.type == BannerType.home) {
        homeBanners.add(newBanner);
      } else if (banner.type == BannerType.splash) {
        splashBanners.add(newBanner);
      }

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to add banner: $e';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Update a banner
  Future<bool> updateBanner(BannerModel banner) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      await _bannerService.updateBanner(banner);

      // Update in the lists
      final index = allBanners.indexWhere((b) => b.id == banner.id);
      if (index != -1) {
        allBanners[index] = banner;
      }

      if (banner.type == BannerType.home) {
        final homeIndex = homeBanners.indexWhere((b) => b.id == banner.id);
        if (homeIndex != -1) {
          homeBanners[homeIndex] = banner;
        }
      } else if (banner.type == BannerType.splash) {
        final splashIndex = splashBanners.indexWhere((b) => b.id == banner.id);
        if (splashIndex != -1) {
          splashBanners[splashIndex] = banner;
        }
      }

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to update banner: $e';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Delete a banner
  Future<bool> deleteBanner(String id) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      await _bannerService.deleteBanner(id);

      // Remove from the lists
      allBanners.removeWhere((b) => b.id == id);
      homeBanners.removeWhere((b) => b.id == id);
      splashBanners.removeWhere((b) => b.id == id);

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to delete banner: $e';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // Toggle banner status
  Future<bool> toggleBannerStatus(String id, bool isActive) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      await _bannerService.toggleBannerStatus(id, isActive);

      // Update in the lists
      final index = allBanners.indexWhere((b) => b.id == id);
      if (index != -1) {
        final updatedBanner = allBanners[index].copyWith(isActive: isActive);
        allBanners[index] = updatedBanner;

        if (updatedBanner.type == BannerType.home) {
          final homeIndex = homeBanners.indexWhere((b) => b.id == id);
          if (homeIndex != -1) {
            homeBanners[homeIndex] = updatedBanner;
          }
        } else if (updatedBanner.type == BannerType.splash) {
          final splashIndex = splashBanners.indexWhere((b) => b.id == id);
          if (splashIndex != -1) {
            splashBanners[splashIndex] = updatedBanner;
          }
        }
      }

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to toggle banner status: $e';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }
}
