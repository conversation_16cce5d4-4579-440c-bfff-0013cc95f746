# Hướng dẫn triển khai Security Rules cho Firestore

## Giới thiệu

Security Rules là cơ chế bảo mật của Firebase Firestore, cho phép bạn kiểm soát quyền truy cập vào dữ liệu của mình. Tài liệu này hướng dẫn cách triển khai Security Rules cho ứng dụng Đớp Phim.

## Phân quyền người dùng

Ứng dụng Đớp Phim có 3 loại người dùng:

1. **Người dùng thông thường**: <PERSON><PERSON> thể đọc dữ liệu công khai và quản lý dữ liệu cá nhân
2. **Admin**: <PERSON><PERSON> quyền quản lý nội dung, người dùng và thông báo
3. **Developer**: <PERSON><PERSON> tất cả quyền của admin và thêm quyền truy cập các tính năng debug

## Cấu trúc Security Rules

File `firestore.rules` đã được tạo với các quy tắc sau:

1. **<PERSON><PERSON><PERSON> thực người dùng**: Kiểm tra xem người dùng đã đăng nhập chưa
2. **Kiểm tra quyền admin**: Kiểm tra xem người dùng có quyền admin không
3. **Kiểm tra chủ sở hữu**: Kiểm tra xem người dùng có đang truy cập dữ liệu của chính họ không
4. **Quy tắc cho từng collection**: Quy định quyền đọc/ghi cho từng collection

## Cách triển khai

### Bước 1: Đăng nhập vào Firebase Console

1. Truy cập [Firebase Console](https://console.firebase.google.com/)
2. Chọn dự án của bạn

### Bước 2: Điều hướng đến Firestore

1. Trong menu bên trái, chọn "Firestore Database"
2. Chọn tab "Rules"

### Bước 3: Cập nhật Rules

1. Xóa rules hiện tại
2. Sao chép nội dung từ file `firestore.rules` và dán vào
3. Nhấn "Publish"

## Cấu trúc dữ liệu

### Collection `users`
- Lưu trữ thông tin người dùng
- Ai cũng có thể đọc
- Chỉ chủ sở hữu hoặc admin có thể ghi

### Collection `user_roles`
- Lưu trữ vai trò của người dùng (user, admin, developer)
- Chỉ chủ sở hữu hoặc admin có thể đọc
- Chỉ admin có thể ghi
- Developer có tất cả quyền của admin và thêm quyền debug

### Collection `banners`
- Lưu trữ banner quảng cáo
- Ai cũng có thể đọc
- Chỉ admin có thể ghi

### Collection `movies`
- Lưu trữ thông tin phim
- Ai cũng có thể đọc
- Chỉ admin có thể ghi

### Collection `notifications`
- Lưu trữ thông báo hệ thống
- Ai cũng có thể đọc
- Chỉ admin có thể ghi

### Collection `favorites`
- Lưu trữ phim yêu thích của người dùng
- Chỉ chủ sở hữu hoặc admin có thể đọc
- Chỉ chủ sở hữu có thể ghi

### Collection `tickets`
- Lưu trữ vé xem phim của người dùng
- Chỉ chủ sở hữu hoặc admin có thể đọc
- Chỉ chủ sở hữu hoặc admin có thể ghi

## Cách thiết lập admin đầu tiên

Để thiết lập admin đầu tiên, bạn cần thực hiện các bước sau:

1. Đăng nhập vào Firebase Console
2. Điều hướng đến Firestore Database
3. Tạo collection `user_roles` nếu chưa có
4. Tạo document mới với ID là UID của người dùng bạn muốn đặt làm admin
5. Thêm field `role` với giá trị `admin`
6. Thêm field `createdAt` và `updatedAt` với giá trị là timestamp hiện tại

Ví dụ:
```json
Collection: user_roles
Document ID: <uid-của-người-dùng>
Fields:
  - role: "admin"
  - createdAt: <timestamp>
  - updatedAt: <timestamp>
```

## Cách thiết lập tài khoản developer

Để thiết lập tài khoản developer, bạn cần thực hiện các bước sau:

1. Đăng nhập vào Firebase Console
2. Điều hướng đến Firestore Database
3. Tạo collection `user_roles` nếu chưa có
4. Tạo document mới với ID là UID của người dùng bạn muốn đặt làm developer
5. Thêm field `role` với giá trị `developer`
6. Thêm field `updatedAt` với giá trị là timestamp hiện tại

Ví dụ:
```json
Collection: user_roles
Document ID: <uid-của-người-dùng>
Fields:
  - role: "developer"
  - updatedAt: <timestamp>
```

Lưu ý: Tài khoản developer sẽ tự động có tất cả quyền của admin và thêm quyền truy cập các tính năng debug.

## Kiểm tra quyền admin

Sau khi thiết lập, bạn có thể kiểm tra quyền admin bằng cách:

1. Đăng nhập vào ứng dụng với tài khoản đã được thiết lập làm admin
2. Vào trang Account
3. Nhấn vào username 7 lần liên tiếp để hiển thị tùy chọn Admin
4. Truy cập trang Admin và kiểm tra các tính năng quản lý

## Lưu ý bảo mật

- Không bao giờ lưu trữ quyền admin trong mã nguồn
- Luôn kiểm tra quyền cả ở client và server
- Thường xuyên kiểm tra logs để phát hiện các truy cập trái phép
- Cập nhật Security Rules khi thêm tính năng mới

## Tài liệu tham khảo

- [Firebase Security Rules Documentation](https://firebase.google.com/docs/firestore/security/get-started)
- [Firebase Security Rules Cookbook](https://firebase.google.com/docs/firestore/security/rules-conditions)
