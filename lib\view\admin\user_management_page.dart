import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/auth_controller.dart';
import '../../models/user_model.dart';

class UserManagementPage extends StatefulWidget {
  const UserManagementPage({Key? key}) : super(key: key);

  @override
  State<UserManagementPage> createState() => _UserManagementPageState();
}

class _UserManagementPageState extends State<UserManagementPage> {
  final AuthController _authController = Get.find<AuthController>();
  final RxList<UserModel> _users = <UserModel>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final users = await _authController.getAllUsers();
      _users.value = users;
    } catch (e) {
      _errorMessage.value = 'Failed to load users: $e';
    } finally {
      _isLoading.value = false;
    }
  }

  // Lấy màu tương ứng với vai trò
  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return Colors.blue;
      case UserRole.developer:
        return Colors.purple;
      case UserRole.user:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  // Lấy tên hiển thị của vai trò
  String _getRoleName(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'Admin';
      case UserRole.developer:
        return 'Developer';
      case UserRole.user:
        return 'User';
      default:
        return 'Unknown';
    }
  }

  Future<void> _setUserRole(UserModel user, UserRole newRole) async {
    _isLoading.value = true;

    try {
      bool success;

      if (newRole == UserRole.admin) {
        success = await _authController.setUserAsAdmin(user.id!);
      } else if (newRole == UserRole.developer) {
        success = await _authController.setUserAsDeveloper(user.id!);
      } else {
        success = await _authController.setUserAsRegular(user.id!);
      }

      if (success) {
        // Update the user in the list
        final index = _users.indexWhere((u) => u.id == user.id);
        if (index != -1) {
          _users[index] = user.copyWith(role: newRole);
        }

        Get.snackbar(
          'Success',
          'User role updated successfully',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'Error',
          'Failed to update user role: ${_authController.errorMessage}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to update user role: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'User Management',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUsers,
          ),
        ],
      ),
      body: SafeArea(
        child: Obx(() {
          if (_isLoading.value) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (_errorMessage.value.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error',
                    style: GoogleFonts.mulish(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      _errorMessage.value,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.mulish(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadUsers,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (_users.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.people_outline,
                    size: 48,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No users found',
                    style: GoogleFonts.mulish(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: _users.length,
            padding: const EdgeInsets.all(16),
            itemBuilder: (context, index) {
              final user = _users[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundImage: NetworkImage(user.photoUrl ??
                        'https://ui-avatars.com/api/?name=${user.name}'),
                  ),
                  title: Text(
                    user.name ?? 'Unknown',
                    style: GoogleFonts.mulish(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(user.email ?? 'No email'),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getRoleColor(user.role),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getRoleName(user.role),
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                  trailing: PopupMenuButton<UserRole>(
                    onSelected: (role) => _setUserRole(user, role),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: UserRole.admin,
                        child: Text('Set as Admin'),
                      ),
                      const PopupMenuItem(
                        value: UserRole.developer,
                        child: Text('Set as Developer'),
                      ),
                      const PopupMenuItem(
                        value: UserRole.user,
                        child: Text('Set as User'),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        }),
      ),
    );
  }
}
