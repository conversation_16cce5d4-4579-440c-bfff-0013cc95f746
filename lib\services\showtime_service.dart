import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/showtime_model.dart';

class ShowtimeService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'showtimes';

  // Get all showtimes
  Future<List<ShowtimeModel>> getAllShowtimes() async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .orderBy('date')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes: $e');
    }
  }

  // Get showtimes by movie
  Future<List<ShowtimeModel>> getShowtimesByMovie(int movieId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('status', isEqualTo: 'active')
          .orderBy('date')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes by movie: $e');
    }
  }

  // Get showtimes by theater
  Future<List<ShowtimeModel>> getShowtimesByTheater(String theaterId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('theaterId', isEqualTo: theaterId)
          .where('status', isEqualTo: 'active')
          .orderBy('date')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes by theater: $e');
    }
  }

  // Get showtimes by date
  Future<List<ShowtimeModel>> getShowtimesByDate(String date) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('date', isEqualTo: date)
          .where('status', isEqualTo: 'active')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes by date: $e');
    }
  }

  // Get showtimes by movie and date
  Future<List<ShowtimeModel>> getShowtimesByMovieAndDate(
      int movieId, String date) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('date', isEqualTo: date)
          .where('status', isEqualTo: 'active')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes by movie and date: $e');
    }
  }

  // Get showtimes by movie and theater
  Future<List<ShowtimeModel>> getShowtimesByMovieAndTheater(
      int movieId, String theaterId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('movieId', isEqualTo: movieId)
          .where('theaterId', isEqualTo: theaterId)
          .where('status', isEqualTo: 'active')
          .orderBy('date')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get showtimes by movie and theater: $e');
    }
  }

  // Get showtime by ID
  Future<ShowtimeModel?> getShowtimeById(String showtimeId) async {
    try {
      final doc =
          await _firestore.collection(_collection).doc(showtimeId).get();

      if (doc.exists) {
        return ShowtimeModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get showtime: $e');
    }
  }

  // Get available showtimes (not full, not cancelled, not ended)
  Future<List<ShowtimeModel>> getAvailableShowtimes() async {
    try {
      final now = DateTime.now();
      final today =
          '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

      final snapshot = await _firestore
          .collection(_collection)
          .where('status', isEqualTo: 'active')
          .where('date', isGreaterThanOrEqualTo: today)
          .orderBy('date')
          .orderBy('time')
          .get();

      return snapshot.docs
          .map((doc) => ShowtimeModel.fromFirestore(doc))
          .where((showtime) => showtime.isBookable)
          .toList();
    } catch (e) {
      throw Exception('Failed to get available showtimes: $e');
    }
  }

  // Book seats
  Future<void> bookSeats(String showtimeId, List<String> seatIds) async {
    try {
      final showtime = await getShowtimeById(showtimeId);
      if (showtime == null) throw Exception('Showtime not found');

      final updatedBookedSeats = [...showtime.bookedSeats, ...seatIds];
      final updatedAvailableSeats = showtime.availableSeats - seatIds.length;

      await _firestore.collection(_collection).doc(showtimeId).update({
        'bookedSeats': updatedBookedSeats,
        'availableSeats': updatedAvailableSeats,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to book seats: $e');
    }
  }

  // Reserve seats temporarily
  Future<void> reserveSeats(String showtimeId, List<String> seatIds) async {
    try {
      final showtime = await getShowtimeById(showtimeId);
      if (showtime == null) throw Exception('Showtime not found');

      final updatedReservedSeats = [...showtime.reservedSeats, ...seatIds];

      await _firestore.collection(_collection).doc(showtimeId).update({
        'reservedSeats': updatedReservedSeats,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to reserve seats: $e');
    }
  }

  // Release reserved seats
  Future<void> releaseReservedSeats(
      String showtimeId, List<String> seatIds) async {
    try {
      final showtime = await getShowtimeById(showtimeId);
      if (showtime == null) throw Exception('Showtime not found');

      final updatedReservedSeats = showtime.reservedSeats
          .where((seat) => !seatIds.contains(seat))
          .toList();

      await _firestore.collection(_collection).doc(showtimeId).update({
        'reservedSeats': updatedReservedSeats,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to release reserved seats: $e');
    }
  }

  // Admin functions
  Future<ShowtimeModel> createShowtime(ShowtimeModel showtime) async {
    return await addShowtime(showtime);
  }

  Future<ShowtimeModel> addShowtime(ShowtimeModel showtime) async {
    try {
      final now = DateTime.now();
      final showtimeData = showtime.copyWith(
        createdAt: now,
        updatedAt: now,
      );

      final docRef = await _firestore
          .collection(_collection)
          .add(showtimeData.toFirestore());

      return showtimeData.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('Failed to add showtime: $e');
    }
  }

  Future<void> updateShowtime(ShowtimeModel showtime) async {
    try {
      final updatedShowtime = showtime.copyWith(updatedAt: DateTime.now());

      await _firestore
          .collection(_collection)
          .doc(showtime.id)
          .update(updatedShowtime.toFirestore());
    } catch (e) {
      throw Exception('Failed to update showtime: $e');
    }
  }

  Future<void> cancelShowtime(String showtimeId) async {
    try {
      await _firestore.collection(_collection).doc(showtimeId).update({
        'status': 'cancelled',
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to cancel showtime: $e');
    }
  }

  Future<void> deleteShowtime(String showtimeId) async {
    try {
      await _firestore.collection(_collection).doc(showtimeId).delete();
    } catch (e) {
      throw Exception('Failed to delete showtime: $e');
    }
  }

  // Stream for real-time updates
  Stream<List<ShowtimeModel>> getShowtimesStream() {
    return _firestore
        .collection(_collection)
        .orderBy('date')
        .orderBy('time')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ShowtimeModel.fromFirestore(doc))
            .toList());
  }

  Stream<List<ShowtimeModel>> getShowtimesByMovieStream(int movieId) {
    return _firestore
        .collection(_collection)
        .where('movieId', isEqualTo: movieId)
        .where('status', isEqualTo: 'active')
        .orderBy('date')
        .orderBy('time')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ShowtimeModel.fromFirestore(doc))
            .toList());
  }

  Stream<ShowtimeModel?> getShowtimeStream(String showtimeId) {
    return _firestore
        .collection(_collection)
        .doc(showtimeId)
        .snapshots()
        .map((doc) => doc.exists ? ShowtimeModel.fromFirestore(doc) : null);
  }

  // Statistics
  Future<Map<String, dynamic>> getShowtimeStatistics() async {
    try {
      final snapshot = await _firestore.collection(_collection).get();

      int totalShowtimes = 0;
      int activeShowtimes = 0;
      int cancelledShowtimes = 0;
      int fullShowtimes = 0;
      final movieCounts = <int, int>{};
      final theaterCounts = <String, int>{};
      final dateCounts = <String, int>{};

      for (final doc in snapshot.docs) {
        final showtime = ShowtimeModel.fromFirestore(doc);
        totalShowtimes++;

        switch (showtime.status) {
          case ShowtimeStatus.active:
            activeShowtimes++;
            break;
          case ShowtimeStatus.cancelled:
            cancelledShowtimes++;
            break;
          case ShowtimeStatus.full:
            fullShowtimes++;
            break;
          case ShowtimeStatus.ended:
            break;
        }

        // Count by movie
        movieCounts[showtime.movieId] =
            (movieCounts[showtime.movieId] ?? 0) + 1;

        // Count by theater
        theaterCounts[showtime.theaterId] =
            (theaterCounts[showtime.theaterId] ?? 0) + 1;

        // Count by date
        dateCounts[showtime.date] = (dateCounts[showtime.date] ?? 0) + 1;
      }

      return {
        'totalShowtimes': totalShowtimes,
        'activeShowtimes': activeShowtimes,
        'cancelledShowtimes': cancelledShowtimes,
        'fullShowtimes': fullShowtimes,
        'movieCounts': movieCounts,
        'theaterCounts': theaterCounts,
        'dateCounts': dateCounts,
      };
    } catch (e) {
      throw Exception('Failed to get showtime statistics: $e');
    }
  }
}
