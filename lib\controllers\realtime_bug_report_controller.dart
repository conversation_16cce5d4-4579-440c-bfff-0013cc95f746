import 'dart:async';
import 'package:get/get.dart';
import '../models/realtime_bug_report_model.dart';
import '../services/realtime_database_service.dart';
import '../services/notification_service.dart';
import 'auth_controller.dart';
import 'notification_controller.dart';

class RealtimeBugReportController extends GetxController {
  final RealtimeDatabaseService _realtimeService = RealtimeDatabaseService();
  final NotificationService _notificationService = NotificationService();
  final AuthController _authController = Get.find<AuthController>();
  final NotificationController _notificationController =
      Get.find<NotificationController>();

  // Observables
  final RxList<RealtimeBugReportModel> _allBugReports =
      <RealtimeBugReportModel>[].obs;
  final RxList<RealtimeBugReportModel> _userBugReports =
      <RealtimeBugReportModel>[].obs;
  final RxList<RealtimeBugReportModel> _filteredBugReports =
      <RealtimeBugReportModel>[].obs;
  final Rx<RealtimeBugReportModel?> _selectedBugReport =
      Rx<RealtimeBugReportModel?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _statusFilter = 'all'.obs;

  // Streams
  StreamSubscription? _allBugReportsSubscription;
  StreamSubscription? _userBugReportsSubscription;
  StreamSubscription? _selectedBugReportSubscription;

  // Getters
  List<RealtimeBugReportModel> get allBugReports => _allBugReports;
  List<RealtimeBugReportModel> get userBugReports => _userBugReports;
  List<RealtimeBugReportModel> get filteredBugReports => _filteredBugReports;
  RealtimeBugReportModel? get selectedBugReport => _selectedBugReport.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  String get statusFilter => _statusFilter.value;

  @override
  void onInit() {
    super.onInit();
    _setupAuthListener();
  }

  @override
  void onClose() {
    _cancelSubscriptions();
    super.onClose();
  }

  // Thiết lập lắng nghe sự thay đổi đăng nhập
  void _setupAuthListener() {
    ever(_authController.userRx, (_) {
      _cancelSubscriptions();
      if (_authController.user != null) {
        fetchBugReports();
      } else {
        _allBugReports.clear();
        _userBugReports.clear();
        _selectedBugReport.value = null;
      }
    });

    // Fetch ngay lập tức nếu đã đăng nhập
    if (_authController.user != null) {
      fetchBugReports();
    }
  }

  // Hủy các subscription
  void _cancelSubscriptions() {
    _allBugReportsSubscription?.cancel();
    _userBugReportsSubscription?.cancel();
    _selectedBugReportSubscription?.cancel();
    _allBugReportsSubscription = null;
    _userBugReportsSubscription = null;
    _selectedBugReportSubscription = null;
  }

  // Lấy danh sách báo cáo lỗi
  Future<void> fetchBugReports() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      if (_authController.isAdmin) {
        // Admin xem tất cả báo cáo
        _allBugReportsSubscription = _realtimeService
            .getAllBugReportsStream()
            .listen(_handleAllBugReports, onError: _handleError);
      } else if (_authController.user?.id != null) {
        // Người dùng chỉ xem báo cáo của mình
        _userBugReportsSubscription = _realtimeService
            .getUserBugReportsStream(_authController.user!.id!)
            .listen(_handleUserBugReports, onError: _handleError);
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tải báo cáo lỗi: $e';
      _isLoading.value = false;
    }
  }

  // Xử lý tất cả báo cáo lỗi
  void _handleAllBugReports(List<RealtimeBugReportModel> bugReports) {
    print('Received ${bugReports.length} bug reports');
    _allBugReports.value = bugReports;
    _applyStatusFilter();
    _isLoading.value = false;
  }

  // Xử lý báo cáo lỗi của người dùng
  void _handleUserBugReports(List<RealtimeBugReportModel> bugReports) {
    print('Received ${bugReports.length} user bug reports');
    _userBugReports.value = bugReports;
    _isLoading.value = false;
  }

  // Áp dụng bộ lọc trạng thái
  void _applyStatusFilter() {
    if (_statusFilter.value == 'all') {
      _filteredBugReports.value = _allBugReports;
    } else {
      _filteredBugReports.value = _allBugReports
          .where((report) => report.status == _statusFilter.value)
          .toList();
    }
  }

  // Đặt bộ lọc trạng thái
  void setStatusFilter(String status) {
    _statusFilter.value = status;
    _applyStatusFilter();
  }

  // Xử lý lỗi
  void _handleError(dynamic error) {
    print('Error in bug report stream: $error');
    _errorMessage.value = 'Lỗi khi tải báo cáo lỗi: $error';
    _isLoading.value = false;
  }

  // Lấy chi tiết báo cáo lỗi theo thời gian thực
  void fetchBugReportDetail(String bugReportId) {
    // Hủy subscription cũ nếu có
    _selectedBugReportSubscription?.cancel();

    // Đặt giá trị loading trước
    _isLoading.value = true;
    _errorMessage.value = '';
    _selectedBugReport.value = null;

    try {
      // Lắng nghe thay đổi theo thời gian thực
      _selectedBugReportSubscription =
          _realtimeService.getBugReportStream(bugReportId).listen(
        (report) {
          _selectedBugReport.value = report;
          _isLoading.value = false;

          if (report == null) {
            _errorMessage.value = 'Không tìm thấy báo cáo lỗi';
          } else {
            _errorMessage.value = '';
          }
        },
        onError: (error) {
          _errorMessage.value = 'Lỗi khi tải chi tiết báo cáo: $error';
          _isLoading.value = false;
        },
      );
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tải chi tiết báo cáo: $e';
      _isLoading.value = false;
    }
  }

  // Tạo báo cáo lỗi mới
  Future<bool> createBugReport({
    required String title,
    required String description,
    Map<String, dynamic>? additionalData,
  }) async {
    if (_authController.user == null) {
      _errorMessage.value = 'Bạn cần đăng nhập để báo cáo lỗi';
      return false;
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // 1. Lưu báo cáo lỗi vào Realtime Database
      final bugReportId = await _realtimeService.createBugReport(
        title: title,
        description: description,
        reportedBy: _authController.user!.id!,
        reportedByName: _authController.user!.name ?? 'Unknown User',
        reportedByEmail: _authController.user!.email,
        additionalData: additionalData,
      );

      if (bugReportId != null) {
        // 2. Tạo thông báo trong Firestore chỉ cho admin và developer
        final adminIds = await _authController.getAdminAndDeveloperIds();
        if (adminIds.isNotEmpty) {
          await _notificationService.createNotification(
            title: '[BUG] $title',
            body: description,
            targetScreen: 'bug_report',
            data: {
              'bugReportId':
                  'rt_$bugReportId', // Thêm tiền tố 'rt_' để phân biệt với báo cáo lỗi Firestore
              'reportedBy': _authController.user!.id!,
              'reportedByName': _authController.user!.name ?? 'Unknown User',
              'reportedByEmail': _authController.user!.email ?? 'No email',
              'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
            },
            isPublic: false, // Thông báo riêng tư chỉ cho admin và developer
            targetUserIds: adminIds, // Chỉ gửi cho admin và developer
          );
        }

        // 3. Làm mới danh sách báo cáo và thông báo
        fetchBugReports();
        _notificationController.fetchNotifications();

        return true;
      } else {
        _errorMessage.value = 'Không thể tạo báo cáo lỗi';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi tạo báo cáo lỗi: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Thêm phản hồi cho báo cáo lỗi
  Future<bool> addResponse({
    required String bugReportId,
    required String message,
  }) async {
    if (_authController.user == null) {
      _errorMessage.value = 'Bạn cần đăng nhập để phản hồi';
      return false;
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // Kiểm tra xem người dùng có quyền phản hồi không
      final report = await _realtimeService.getBugReportById(bugReportId);

      if (report == null) {
        _errorMessage.value = 'Không tìm thấy báo cáo lỗi';
        return false;
      }

      // Nếu là người dùng thông thường (không phải admin/dev) và báo cáo không cho phép phản hồi
      final isAdminOrDev =
          _authController.isAdmin || _authController.userRole == 'developer';
      if (!isAdminOrDev && !report.allowUserResponse) {
        _errorMessage.value = 'Báo cáo lỗi này không cho phép phản hồi';
        return false;
      }

      // 1. Thêm phản hồi vào Realtime Database
      final success = await _realtimeService.addBugResponse(
        bugReportId: bugReportId,
        responderId: _authController.user!.id!,
        responderName: _authController.user!.name ?? 'Unknown User',
        message: message,
        isFromAdmin: _authController.isAdmin,
        isFromDeveloper: _authController.userRole == 'developer',
      );

      if (success) {
        // 3. Tạo thông báo phản hồi trong Firestore
        // Nếu là admin/dev phản hồi cho người dùng
        if (isAdminOrDev && report.reportedBy != _authController.user!.id!) {
          await _notificationService.createNotification(
            title: 'Phản hồi cho báo cáo lỗi của bạn',
            body: message,
            targetScreen: 'bug_report_detail',
            data: {'bugReportId': 'rt_$bugReportId'},
            isPublic: false,
            targetUserIds: [report.reportedBy],
          );
        }
        // Nếu là người dùng phản hồi, thông báo cho admin
        else if (!isAdminOrDev) {
          final adminIds = await _authController.getAdminAndDeveloperIds();
          if (adminIds.isNotEmpty) {
            await _notificationService.createNotification(
              title: 'Phản hồi mới cho báo cáo lỗi',
              body: '${_authController.user!.name} đã phản hồi: $message',
              targetScreen: 'bug_report_detail',
              data: {'bugReportId': 'rt_$bugReportId'},
              isPublic: false,
              targetUserIds: adminIds,
            );
          }
        }

        // 4. Làm mới chi tiết báo cáo và thông báo
        // fetchBugReportDetail sẽ tự động cập nhật qua Stream
        _notificationController.fetchNotifications();

        return true;
      } else {
        _errorMessage.value = 'Không thể thêm phản hồi';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi thêm phản hồi: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Cập nhật trạng thái báo cáo lỗi
  Future<bool> updateBugStatus({
    required String bugReportId,
    required String newStatus,
  }) async {
    if (_authController.user == null) {
      _errorMessage.value = 'Bạn cần đăng nhập để cập nhật trạng thái';
      return false;
    }

    if (!_authController.isAdmin && _authController.userRole != 'developer') {
      _errorMessage.value = 'Bạn không có quyền cập nhật trạng thái';
      return false;
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final isDeveloper = _authController.userRole == 'developer';

      // 1. Cập nhật trạng thái trong Realtime Database
      final success = await _realtimeService.updateBugStatus(
        bugReportId: bugReportId,
        newStatus: newStatus,
        updatedBy: _authController.user!.id!,
        updatedByName: _authController.user!.name ?? 'Unknown User',
        isFromDeveloper: isDeveloper,
      );

      if (success) {
        // 2. Lấy thông tin báo cáo lỗi
        final report = await _realtimeService.getBugReportById(bugReportId);

        if (report != null) {
          // 3. Tạo thông báo cập nhật trạng thái trong Firestore

          // Thông báo cho người dùng
          await _notificationService.createNotification(
            title: 'Cập nhật trạng thái báo cáo lỗi',
            body:
                'Báo cáo lỗi của bạn đã được cập nhật thành: ${_getStatusDisplayName(newStatus)}',
            targetScreen: 'bug_report_detail',
            data: {'bugReportId': 'rt_$bugReportId'},
            isPublic: false,
            targetUserIds: [report.reportedBy],
          );

          // Nếu là developer cập nhật, thông báo cho admin
          if (isDeveloper) {
            final adminIds = await _authController.getAdminAndDeveloperIds();
            if (adminIds.isNotEmpty) {
              await _notificationService.createNotification(
                title: 'Developer đã cập nhật trạng thái báo cáo lỗi',
                body:
                    '${_authController.user!.name} đã cập nhật trạng thái báo cáo lỗi "${report.title}" thành ${_getStatusDisplayName(newStatus)}',
                targetScreen: 'bug_report_detail',
                data: {'bugReportId': 'rt_$bugReportId'},
                isPublic: false,
                targetUserIds: adminIds
                    .where((id) => id != _authController.user!.id!)
                    .toList(),
              );
            }
          }
        }

        // 4. Làm mới chi tiết báo cáo và thông báo
        // fetchBugReportDetail sẽ tự động cập nhật qua Stream
        _notificationController.fetchNotifications();

        return true;
      } else {
        _errorMessage.value = 'Không thể cập nhật trạng thái';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi cập nhật trạng thái: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Hàm hỗ trợ lấy tên hiển thị của trạng thái
  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'accepted':
        return 'Đã nhận';
      case 'inProgress':
        return 'Đang fix';
      case 'fixed':
        return 'Đã fix';
      default:
        return 'Chưa nhận';
    }
  }

  // Làm mới báo cáo lỗi
  void refreshBugReports() {
    _cancelSubscriptions();
    _allBugReports.clear();
    _userBugReports.clear();
    fetchBugReports();
  }

  // Cập nhật trạng thái cho phép phản hồi của người dùng
  Future<bool> updateAllowUserResponse({
    required String bugReportId,
    required bool allowUserResponse,
  }) async {
    if (_authController.user == null) {
      _errorMessage.value = 'Bạn cần đăng nhập để cập nhật trạng thái';
      return false;
    }

    if (!_authController.isAdmin && _authController.userRole != 'developer') {
      _errorMessage.value = 'Bạn không có quyền cập nhật trạng thái';
      return false;
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final isDeveloper = _authController.userRole == 'developer';

      // 1. Cập nhật trạng thái trong Realtime Database
      final success = await _realtimeService.updateAllowUserResponse(
        bugReportId: bugReportId,
        allowUserResponse: allowUserResponse,
        updatedBy: _authController.user!.id!,
        updatedByName: _authController.user!.name ?? 'Unknown User',
        isFromDeveloper: isDeveloper,
      );

      if (success) {
        // 2. Lấy thông tin báo cáo lỗi
        final report = await _realtimeService.getBugReportById(bugReportId);

        if (report != null) {
          // 3. Tạo thông báo cập nhật trạng thái trong Firestore
          final message = allowUserResponse
              ? 'Đã cho phép người dùng phản hồi'
              : 'Đã tắt phản hồi của người dùng';

          // Thông báo cho người dùng
          await _notificationService.createNotification(
            title: 'Cập nhật trạng thái báo cáo lỗi',
            body: message,
            targetScreen: 'bug_report_detail',
            data: {'bugReportId': 'rt_$bugReportId'},
            isPublic: false,
            targetUserIds: [report.reportedBy],
          );
        }

        // 4. Làm mới chi tiết báo cáo và thông báo
        // fetchBugReportDetail sẽ tự động cập nhật qua Stream
        _notificationController.fetchNotifications();

        return true;
      } else {
        _errorMessage.value = 'Không thể cập nhật trạng thái';
        return false;
      }
    } catch (e) {
      _errorMessage.value = 'Lỗi khi cập nhật trạng thái: $e';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Dừng lắng nghe chi tiết báo cáo lỗi
  void stopListeningToBugReportDetail() {
    _selectedBugReportSubscription?.cancel();
    _selectedBugReportSubscription = null;
    _selectedBugReport.value = null;
  }

  // Xóa lỗi
  void clearError() {
    _errorMessage.value = '';
  }
}
