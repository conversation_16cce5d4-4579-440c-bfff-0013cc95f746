class ImportResult<T> {
  final List<T> successItems;
  final List<ImportError> errors;
  final int totalProcessed;

  ImportResult({
    required this.successItems,
    required this.errors,
    required this.totalProcessed,
  });

  bool get hasErrors => errors.isNotEmpty;
  int get successCount => successItems.length;
  int get errorCount => errors.length;
  double get successRate =>
      totalProcessed > 0 ? successCount / totalProcessed : 0.0;
}

class ImportError {
  final int row;
  final String field;
  final String message;
  final dynamic value;

  ImportError({
    required this.row,
    required this.field,
    required this.message,
    this.value,
  });

  @override
  String toString() {
    return 'Row $row, Field "$field": $message${value != null ? ' (Value: $value)' : ''}';
  }
}

class MovieImportTemplate {
  static const List<String> requiredFields = [
    'title',
    'genres',
    'status',
  ];

  static const List<String> optionalFields = [
    'originalTitle',
    'subtitle',
    'overview',
    'posterPath',
    'backdropPath',
    'trailerUrl',
    'releaseDate',
    'runtime',
    'voteAverage',
    'voteCount',
    'popularity',
    'ageRating',
    'language',
    'country',
    'director',
    'cast',
    'isActive',
    'isHomeBanner',
    'isSplashBanner',
    'bannerOrder',
  ];

  static const List<String> allFields = [
    ...requiredFields,
    ...optionalFields,
  ];

  static const Map<String, String> fieldDescriptions = {
    'title': 'Tên phim (bắt buộc)',
    'originalTitle': 'Tên gốc',
    'subtitle': 'Phụ đề',
    'overview': 'Mô tả phim',
    'posterPath': 'URL poster',
    'backdropPath': 'URL backdrop',
    'trailerUrl': 'URL trailer',
    'genres': 'Thể loại (phân cách bằng dấu phẩy, bắt buộc)',
    'releaseDate': 'Ngày phát hành (YYYY-MM-DD)',
    'runtime': 'Thời lượng (phút)',
    'voteAverage': 'Điểm đánh giá (0-10)',
    'voteCount': 'Số lượt đánh giá',
    'popularity': 'Độ phổ biến',
    'ageRating': 'Phân loại độ tuổi',
    'language': 'Ngôn ngữ',
    'country': 'Quốc gia',
    'director': 'Đạo diễn',
    'cast': 'Diễn viên (JSON format)',
    'status': 'Trạng thái (now_playing/upcoming/ended, bắt buộc)',
    'isActive': 'Hoạt động (true/false)',
    'isHomeBanner': 'Banner trang chủ (true/false)',
    'isSplashBanner': 'Banner khởi động (true/false)',
    'bannerOrder': 'Thứ tự banner (số)',
  };

  static List<Map<String, dynamic>> getSampleData() {
    return [
      {
        'title': 'Avengers: Endgame',
        'originalTitle': 'Avengers: Endgame',
        'overview': 'The grave course of events set in motion by Thanos...',
        'genres': 'Action, Adventure, Drama',
        'releaseDate': '2019-04-26',
        'runtime': 181,
        'voteAverage': 8.4,
        'ageRating': 'PG-13',
        'language': 'English',
        'country': 'USA',
        'director': 'Anthony Russo, Joe Russo',
        'status': 'now_playing',
        'isActive': true,
        'isHomeBanner': false,
        'isSplashBanner': false,
      },
      {
        'title': 'Spider-Man: No Way Home',
        'originalTitle': 'Spider-Man: No Way Home',
        'overview': 'Peter Parker seeks help from Doctor Strange...',
        'genres': 'Action, Adventure, Fantasy',
        'releaseDate': '2021-12-17',
        'runtime': 148,
        'voteAverage': 8.2,
        'ageRating': 'PG-13',
        'language': 'English',
        'country': 'USA',
        'director': 'Jon Watts',
        'status': 'upcoming',
        'isActive': true,
        'isHomeBanner': true,
        'isSplashBanner': false,
        'bannerOrder': 1,
      },
    ];
  }
}

class TheaterImportTemplate {
  static const List<String> requiredFields = [
    'name',
    'address_street',
    'address_city',
    'phoneNumber',
  ];

  static const List<String> optionalFields = [
    'address_district',
    'address_province',
    'latitude',
    'longitude',
    'email',
    'facilities',
    'operatingHours',
    'isActive',
  ];

  static const List<String> allFields = [
    ...requiredFields,
    ...optionalFields,
  ];

  static const Map<String, String> fieldDescriptions = {
    'name': 'Tên rạp (bắt buộc)',
    'address_street': 'Địa chỉ đường (bắt buộc)',
    'address_district': 'Quận/Huyện',
    'address_province': 'Tỉnh/Thành phố',
    'address_city': 'Thành phố (bắt buộc)',
    'latitude': 'Vĩ độ',
    'longitude': 'Kinh độ',
    'phoneNumber': 'Số điện thoại (bắt buộc)',
    'email': 'Email',
    'facilities': 'Tiện ích (phân cách bằng dấu phẩy)',
    'operatingHours': 'Giờ hoạt động (JSON format)',
    'isActive': 'Hoạt động (true/false)',
  };

  static List<Map<String, dynamic>> getSampleData() {
    return [
      {
        'name': 'CGV Vincom Center',
        'address_street': '72 Lê Thánh Tôn',
        'address_district': 'Quận 1',
        'address_province': 'Hồ Chí Minh',
        'address_city': 'Hồ Chí Minh',
        'latitude': 10.7769,
        'longitude': 106.7009,
        'phoneNumber': '028-3822-5555',
        'email': '<EMAIL>',
        'facilities': 'parking, food_court, 3d, imax',
        'operatingHours':
            '{"monday":{"open":"09:00","close":"23:00"},"tuesday":{"open":"09:00","close":"23:00"}}',
        'isActive': true,
      },
      {
        'name': 'Lotte Cinema Landmark 81',
        'address_street': '720A Điện Biên Phủ',
        'address_district': 'Bình Thạnh',
        'address_province': 'Hồ Chí Minh',
        'address_city': 'Hồ Chí Minh',
        'latitude': 10.7951,
        'longitude': 106.7218,
        'phoneNumber': '028-3512-6789',
        'facilities': 'parking, food_court, vip',
        'isActive': true,
      },
    ];
  }
}

class ScreenImportTemplate {
  static const List<String> requiredFields = [
    'theaterId',
    'name',
    'type',
    'totalSeats',
    'rows',
    'seatsPerRow',
  ];

  static const List<String> optionalFields = [
    'amenities',
    'isActive',
  ];

  static const List<String> allFields = [
    ...requiredFields,
    ...optionalFields,
  ];

  static const Map<String, String> fieldDescriptions = {
    'theaterId':
        'ID rạp chiếu (tự động điền nếu trống khi import từ trang quản lý rạp)',
    'name': 'Tên phòng chiếu (bắt buộc)',
    'type': 'Loại phòng (standard/premium/imax/vip/dolby, bắt buộc)',
    'totalSeats': 'Tổng số ghế (bắt buộc)',
    'rows': 'Số hàng ghế (bắt buộc)',
    'seatsPerRow': 'Số ghế mỗi hàng (bắt buộc)',
    'amenities': 'Tiện nghi (phân cách bằng dấu phẩy)',
    'isActive': 'Hoạt động (true/false)',
  };

  static List<Map<String, dynamic>> getSampleData() {
    return [
      {
        // theaterId will be auto-filled if importing from theater management page
        'name': 'Screen 1',
        'type': 'standard',
        'totalSeats': 120,
        'rows': 10,
        'seatsPerRow': 12,
        'amenities': 'air_conditioning, dolby_atmos',
        'isActive': true,
      },
      {
        // theaterId can be specified if needed
        'theaterId': 'specific_theater_id',
        'name': 'Screen 2',
        'type': 'premium',
        'totalSeats': 80,
        'rows': 8,
        'seatsPerRow': 10,
        'amenities': 'air_conditioning, dolby_atmos, reclining_seats',
        'isActive': true,
      },
    ];
  }
}
