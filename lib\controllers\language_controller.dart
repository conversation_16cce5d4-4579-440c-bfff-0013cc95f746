import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageController extends GetxController {
  static const String languageCode = 'languageCode';
  static const String countryCode = 'countryCode';

  final RxString _currentLanguageCode = 'en'.obs;
  final RxString _currentCountryCode = 'US'.obs;

  String get currentLanguageCode => _currentLanguageCode.value;
  String get currentCountryCode => _currentCountryCode.value;

  Locale get currentLocale =>
      Locale(_currentLanguageCode.value, _currentCountryCode.value);

  @override
  void onInit() {
    super.onInit();
    loadSavedLanguage();
  }

  Future<void> loadSavedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLanguageCode = prefs.getString(languageCode);
    final savedCountryCode = prefs.getString(countryCode);

    if (savedLanguageCode != null && savedCountryCode != null) {
      _currentLanguageCode.value = savedLanguageCode;
      _currentCountryCode.value = savedCountryCode;
      updateLocale(const Locale('en', 'US'));
      updateLocale(Locale(savedLanguageCode, savedCountryCode));
    }
  }

  Future<void> changeLanguage(String languageCode, String countryCode) async {
    final locale = Locale(languageCode, countryCode);

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(LanguageController.languageCode, languageCode);
    await prefs.setString(LanguageController.countryCode, countryCode);

    _currentLanguageCode.value = languageCode;
    _currentCountryCode.value = countryCode;

    updateLocale(locale);
  }

  void updateLocale(Locale locale) {
    Get.updateLocale(locale);
  }

  void toggleLanguage() {
    if (_currentLanguageCode.value == 'en') {
      changeLanguage('vi', 'VN');
    } else {
      changeLanguage('en', 'US');
    }
  }

  bool get isEnglish => _currentLanguageCode.value == 'en';
}
