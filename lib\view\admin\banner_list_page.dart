import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/banner_controller.dart';
import '../../models/banner_model.dart';
import 'banner_edit_page.dart';

class BannerListPage extends StatefulWidget {
  const BannerListPage({Key? key}) : super(key: key);

  @override
  State<BannerListPage> createState() => _BannerListPageState();
}

class _BannerListPageState extends State<BannerListPage> {
  late BannerController _bannerController;
  BannerType? _selectedType;

  @override
  void initState() {
    super.initState();
    // Initialize the controller if it doesn't exist
    _bannerController = Get.put(BannerController());
    _bannerController.fetchAllBanners();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Banner Management'),
        centerTitle: true,
        actions: [
          PopupMenuButton<BannerType?>(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter by type',
            onSelected: (type) {
              setState(() {
                _selectedType = type;
              });
              _bannerController.setFilter(type);
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: null,
                child: Text('All Banners'),
              ),
              const PopupMenuItem(
                value: BannerType.home,
                child: Text('Home Banners'),
              ),
              const PopupMenuItem(
                value: BannerType.splash,
                child: Text('Splash Banners'),
              ),
            ],
          ),
        ],
      ),
      body: Obx(() {
        if (_bannerController.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_bannerController.errorMessage.value.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Error: ${_bannerController.errorMessage.value}',
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => _bannerController.fetchAllBanners(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final banners = _getFilteredBanners();

        if (banners.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.image_not_supported,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                Text(
                  _selectedType == null
                      ? 'No banners found'
                      : 'No ${_selectedType.toString().split('.').last} banners found',
                  style: const TextStyle(fontSize: 18),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => _navigateToEditPage(null),
                  child: const Text('Add Banner'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => _bannerController.fetchAllBanners(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: banners.length,
            itemBuilder: (context, index) {
              final banner = banners[index];
              return _buildBannerCard(banner);
            },
          ),
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToEditPage(null),
        tooltip: 'Add Banner',
        child: const Icon(Icons.add),
      ),
    );
  }

  List<BannerModel> _getFilteredBanners() {
    if (_selectedType == null) {
      return _bannerController.allBanners;
    } else if (_selectedType == BannerType.home) {
      return _bannerController.homeBanners;
    } else {
      return _bannerController.splashBanners;
    }
  }

  Widget _buildBannerCard(BannerModel banner) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Banner image
          ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: AspectRatio(
              aspectRatio: 16 / 9,
              child: Image.network(
                banner.imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[800],
                    child: const Center(
                      child: Icon(
                        Icons.image_not_supported,
                        color: Colors.white54,
                        size: 50,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // Banner details
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        banner.title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: banner.type == BannerType.home
                            ? Colors.blue.withOpacity(0.2)
                            : Colors.purple.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        banner.type == BannerType.home ? 'Home' : 'Splash',
                        style: TextStyle(
                          color: banner.type == BannerType.home
                              ? Colors.blue
                              : Colors.purple,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
                if (banner.description != null &&
                    banner.description!.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      banner.description!,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Status indicator
                    Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: banner.isActive ? Colors.green : Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          banner.isActive ? 'Active' : 'Inactive',
                          style: TextStyle(
                            color: banner.isActive ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),

                    // Action buttons
                    Row(
                      children: [
                        // Toggle status button
                        IconButton(
                          icon: Icon(
                            banner.isActive
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: banner.isActive ? Colors.red : Colors.green,
                          ),
                          tooltip: banner.isActive ? 'Deactivate' : 'Activate',
                          onPressed: () => _toggleBannerStatus(banner),
                        ),

                        // Edit button
                        IconButton(
                          icon: const Icon(
                            Icons.edit,
                            color: Colors.blue,
                          ),
                          tooltip: 'Edit',
                          onPressed: () => _navigateToEditPage(banner),
                        ),

                        // Delete button
                        IconButton(
                          icon: const Icon(
                            Icons.delete,
                            color: Colors.red,
                          ),
                          tooltip: 'Delete',
                          onPressed: () => _confirmDelete(banner),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToEditPage(BannerModel? banner) async {
    final result = await Get.to(() => BannerEditPage(banner: banner));
    if (result == true) {
      _bannerController.fetchAllBanners();
    }
  }

  void _toggleBannerStatus(BannerModel banner) async {
    final newStatus = !banner.isActive;
    final success = await _bannerController.toggleBannerStatus(
      banner.id,
      newStatus,
    );

    if (success) {
      Get.snackbar(
        'Success',
        'Banner ${newStatus ? 'activated' : 'deactivated'} successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void _confirmDelete(BannerModel banner) {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Banner'),
        content: Text(
          'Are you sure you want to delete the banner "${banner.title}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              final success = await _bannerController.deleteBanner(banner.id);

              if (success) {
                Get.snackbar(
                  'Success',
                  'Banner deleted successfully',
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
