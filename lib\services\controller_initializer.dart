import 'package:get/get.dart';
import '../controllers/movie_controller.dart';
import '../controllers/favorite_controller.dart';
import '../controllers/ticket_controller.dart';
import '../controllers/booking_controller.dart';
import '../controllers/schedule_controller.dart';
import '../controllers/banner_controller.dart';
import '../controllers/notification_controller.dart';
import '../controllers/bug_report_controller.dart';
import '../controllers/realtime_notification_controller.dart';
import '../controllers/realtime_bug_report_controller.dart';
import '../bindings/realtime_database_binding.dart';
import '../utils/developer_mode.dart';

class ControllerInitializer {
  static bool _isInitialized = false;
  
  /// Initialize all controllers lazily after successful authentication
  static Future<void> initializeControllers() async {
    if (_isInitialized) return;
    
    try {
      // Initialize core controllers first
      Get.put(MovieController());
      Get.put(FavoriteController());
      Get.put(TicketController());
      
      // Initialize booking related controllers
      Get.put(BookingController());
      Get.put(ScheduleController());
      
      // Initialize UI controllers
      Get.put(BannerController());
      Get.put(NotificationController());
      Get.put(BugReportController());
      
      // Initialize Realtime Database controllers
      Get.put(RealtimeNotificationController());
      Get.put(RealtimeBugReportController());
      
      // Initialize developer mode
      Get.put(DeveloperMode());
      
      // Initialize bindings
      RealtimeDatabaseBinding().dependencies();
      
      _isInitialized = true;
    } catch (e) {
      // Log error but don't throw to prevent app crash
      print('Error initializing controllers: $e');
    }
  }
  
  /// Reset initialization state (useful for logout)
  static void reset() {
    _isInitialized = false;
  }
  
  /// Check if controllers are initialized
  static bool get isInitialized => _isInitialized;
}
