import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../controllers/notification_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/notification_model.dart';
import '../../models/user_model.dart';
import '../../services/notification_service.dart';
import '../../utils/role_helper.dart';

class NewNotificationPage extends StatelessWidget {
  const NewNotificationPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Đảm bảo controller đã được khởi tạo
    final controller = Get.find<NotificationController>();

    // Gọi fetchNotifications khi màn hình được hiển thị
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.fetchNotifications();
    });

    return Scaffold(
      // Sử dụng gradient giống với app
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xff4B79A1), Color(0xff283E51)],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header với tiêu đề và các nút
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: [
                          // Nút làm mới
                          IconButton(
                            icon: const Icon(Icons.refresh, color: Colors.blue),
                            tooltip: 'Làm mới',
                            onPressed: () {
                              controller.fetchNotifications();
                            },
                          ),
                          
                          Obx(() => controller.unreadCount > 0
                              ? TextButton(
                                  onPressed: () {
                                    controller.markAllAsRead();
                                  },
                                  child: Text(
                                    'Đọc tất cả (${controller.unreadCount})',
                                    style: GoogleFonts.mulish(
                                      fontSize: 14,
                                      color: Colors.blue,
                                    ),
                                  ),
                                )
                              : const SizedBox.shrink()),
                          // Nút tạo thông báo mẫu (chỉ hiển thị cho admin và developer)
                          if (RoleHelper.hasAdminAccess())
                            TextButton(
                              onPressed: () async {
                                // Tạo thông báo mẫu
                                final notificationService =
                                    NotificationService();
                                await notificationService
                                    .createSampleNotifications();

                                // Làm mới danh sách thông báo
                                controller.fetchNotifications();

                                // Hiển thị thông báo thành công
                                Get.snackbar(
                                  'Thành công',
                                  'Đã tạo thông báo mẫu',
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor: Colors.green,
                                  colorText: Colors.white,
                                );
                              },
                              child: Text(
                                'Tạo mẫu',
                                style: GoogleFonts.mulish(
                                  fontSize: 14,
                                  color: Colors.orange,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Expanded(
                  child: Obx(() {
                    if (controller.isLoading) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (controller.notifications.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.notifications_off_outlined,
                              size: 80,
                              color: Colors.white.withOpacity(0.5),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Không có thông báo nào',
                              style: GoogleFonts.mulish(
                                fontSize: 18,
                                color: Colors.white.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return RefreshIndicator(
                      onRefresh: () => controller.fetchNotifications(),
                      child: ListView.builder(
                        itemCount: controller.notifications.length,
                        itemBuilder: (context, index) {
                          final viewModel = controller.notifications[index];
                          return _buildNotificationItem(
                              context, viewModel, controller);
                        },
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationItem(
    BuildContext context,
    UserNotificationViewModel viewModel,
    NotificationController controller,
  ) {
    final notification = viewModel.notification;
    final userNotification = viewModel.userNotification;
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    // Xác định icon và màu sắc dựa trên loại thông báo
    IconData iconData = Icons.notifications;
    Color iconColor = Colors.blue;

    if (notification.targetScreen == 'movie_detail') {
      iconData = Icons.movie;
      iconColor = Colors.purple;
    } else if (notification.targetScreen == 'ticket') {
      iconData = Icons.confirmation_number;
      iconColor = Colors.green;
    } else if (notification.targetScreen == 'promo') {
      iconData = Icons.local_offer;
      iconColor = Colors.orange;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: userNotification.isRead
          ? Colors.white.withOpacity(0.05)
          : Colors.blue.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // Đánh dấu là đã đọc khi nhấp vào
          controller.markAsRead(notification.id);
          _showNotificationDetails(context, viewModel);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Phần header với icon và tiêu đề
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: iconColor.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      iconData,
                      color: iconColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                notification.title,
                                style: GoogleFonts.mulish(
                                  fontSize: 16,
                                  fontWeight: userNotification.isRead
                                      ? FontWeight.normal
                                      : FontWeight.bold,
                                  color: Colors.white,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (!userNotification.isRead)
                              Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  color: Colors.blue,
                                  shape: BoxShape.circle,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          dateFormat.format(notification.createdAt),
                          style: GoogleFonts.mulish(
                            fontSize: 12,
                            color: Colors.white54,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Nút xóa thông báo (chỉ hiển thị cho admin và developer hoặc thông báo của chính người dùng)
                  if (RoleHelper.hasAdminAccess() ||
                      notification.isPublic == false)
                    IconButton(
                      icon: const Icon(Icons.delete_outline, color: Colors.red),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      onPressed: () {
                        controller.markAsDeleted(notification.id);
                      },
                    ),
                ],
              ),

              // Phần nội dung
              Padding(
                padding: const EdgeInsets.only(left: 40, top: 8, right: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      notification.body,
                      style: GoogleFonts.mulish(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // Hiển thị ảnh nếu có
                    if (notification.imageUrl != null &&
                        notification.imageUrl!.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          constraints: const BoxConstraints(
                            maxHeight: 120,
                            maxWidth:
                                200, // Giới hạn chiều rộng để ảnh dọc không bị quá rộng
                          ),
                          child: Image.network(
                            notification.imageUrl!,
                            fit: BoxFit
                                .contain, // Thay đổi từ cover sang contain
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                height: 80,
                                width: 120,
                                color: Colors.grey.withOpacity(0.2),
                                child: const Center(
                                  child: Icon(
                                    Icons.error_outline,
                                    color: Colors.white54,
                                  ),
                                ),
                              );
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Container(
                                height: 80,
                                width: 120,
                                color: Colors.grey.withOpacity(0.2),
                                child: Center(
                                  child: CircularProgressIndicator(
                                    value: loadingProgress.expectedTotalBytes !=
                                            null
                                        ? loadingProgress
                                                .cumulativeBytesLoaded /
                                            loadingProgress.expectedTotalBytes!
                                        : null,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],

                    // Hiển thị badge "Riêng tư" nếu thông báo không công khai
                    if (!notification.isPublic) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Riêng tư',
                          style: GoogleFonts.mulish(
                            fontSize: 10,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Hiển thị hộp thoại phản hồi bug
  void _showBugResponseDialog(
      BuildContext context, NotificationModel bugReport) {
    final responseController = TextEditingController();
    final authController = Get.find<AuthController>();
    final notificationService = NotificationService();

    // Lấy thông tin người báo bug từ data
    final reportedBy = bugReport.data?['reportedBy'] as String? ?? '';
    final reportedByName =
        bugReport.data?['reportedByName'] as String? ?? 'Unknown User';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xff2B5876),
        title: Text(
          'Phản hồi báo lỗi',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Báo cáo: ${bugReport.title}',
                style: GoogleFonts.mulish(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Từ: $reportedByName',
                style: GoogleFonts.mulish(
                  color: Colors.white70,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: responseController,
                decoration: InputDecoration(
                  labelText: 'Phản hồi của bạn',
                  labelStyle: GoogleFonts.mulish(color: Colors.white70),
                  border: const OutlineInputBorder(),
                  enabledBorder: OutlineInputBorder(
                    borderSide:
                        BorderSide(color: Colors.white.withOpacity(0.3)),
                  ),
                ),
                maxLines: 5,
                style: GoogleFonts.mulish(color: Colors.white),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Checkbox(
                    value: true,
                    onChanged: (value) {},
                  ),
                  Expanded(
                    child: Text(
                      'Gửi thông báo cảm ơn đến người dùng',
                      style: GoogleFonts.mulish(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Hủy',
              style: GoogleFonts.mulish(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              if (responseController.text.isEmpty) {
                Get.snackbar(
                  'Lỗi',
                  'Vui lòng nhập phản hồi',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              // Đóng hộp thoại
              Navigator.pop(context);

              try {
                // Gửi thông báo cảm ơn đến người dùng
                if (reportedBy.isNotEmpty) {
                  await notificationService.createNotification(
                    title: 'Cảm ơn bạn đã báo lỗi',
                    body:
                        'Chúng tôi đã nhận được báo cáo lỗi của bạn và đang xử lý. Phản hồi: ${responseController.text.trim()}',
                    targetScreen: null,
                    isPublic: false,
                    targetUserIds: [reportedBy],
                  );
                }

                // Nếu người phản hồi là developer, gửi thông báo đến admin
                if (authController.userRole == 'developer') {
                  try {
                    // Lọc ra chỉ các admin (không bao gồm developer)
                    final users = await authController.getAllUsers();
                    final onlyAdminIds = users
                        .where((user) => user.isAdmin && !user.isDeveloper)
                        .map((user) => user.id ?? '')
                        .where((id) => id.isNotEmpty)
                        .toList();

                    if (onlyAdminIds.isNotEmpty) {
                      await notificationService.createNotification(
                        title: '[BUG FIXED] ${bugReport.title}',
                        body:
                            'Developer đã xử lý lỗi: ${responseController.text.trim()}',
                        targetScreen: null,
                        isPublic: false,
                        targetUserIds: onlyAdminIds,
                      );
                    } else {
                      // Nếu không tìm thấy admin nào, tạo thông báo công khai
                      await notificationService.createNotification(
                        title: '[BUG FIXED] ${bugReport.title}',
                        body:
                            'Developer đã xử lý lỗi: ${responseController.text.trim()}',
                        targetScreen: null,
                        isPublic: true, // Thông báo công khai
                      );
                    }
                  } catch (adminError) {
                    print('Error getting admin IDs: $adminError');
                    // Nếu có lỗi, tạo thông báo công khai
                    await notificationService.createNotification(
                      title: '[BUG FIXED] ${bugReport.title}',
                      body:
                          'Developer đã xử lý lỗi: ${responseController.text.trim()}',
                      targetScreen: null,
                      isPublic: true, // Thông báo công khai
                    );
                  }
                }

                Get.snackbar(
                  'Thành công',
                  'Đã gửi phản hồi',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } catch (e) {
                Get.snackbar(
                  'Lỗi',
                  'Không thể gửi phản hồi: $e',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
            ),
            child: Text(
              'Gửi',
              style: GoogleFonts.mulish(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showNotificationDetails(
    BuildContext context,
    UserNotificationViewModel viewModel,
  ) {
    final notification = viewModel.notification;
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    // Xác định icon và màu sắc dựa trên loại thông báo
    IconData iconData = Icons.notifications;
    Color iconColor = Colors.blue;

    if (notification.targetScreen == 'movie_detail') {
      iconData = Icons.movie;
      iconColor = Colors.purple;
    } else if (notification.targetScreen == 'ticket') {
      iconData = Icons.confirmation_number;
      iconColor = Colors.green;
    } else if (notification.targetScreen == 'promo') {
      iconData = Icons.local_offer;
      iconColor = Colors.orange;
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0xff2B5876),
      isScrollControlled: true, // Cho phép cuộn nếu nội dung dài
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6, // Kích thước ban đầu
          minChildSize: 0.3, // Kích thước tối thiểu
          maxChildSize: 0.95, // Kích thước tối đa
          expand: false,
          builder: (context, scrollController) {
            return SingleChildScrollView(
              controller: scrollController,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Thanh kéo ở đầu
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),

                    // Header với icon và tiêu đề
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: iconColor.withOpacity(0.2),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            iconData,
                            color: iconColor,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            notification.title,
                            style: GoogleFonts.mulish(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Thời gian và badge
                    Padding(
                      padding: const EdgeInsets.only(left: 50),
                      child: Row(
                        children: [
                          Text(
                            dateFormat.format(notification.createdAt),
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: Colors.white54,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (!notification.isPublic)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.blue.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'Riêng tư',
                                style: GoogleFonts.mulish(
                                  fontSize: 12,
                                  color: Colors.blue,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Nội dung thông báo
                    Text(
                      notification.body,
                      style: GoogleFonts.mulish(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),

                    // Hiển thị ảnh nếu có
                    if (notification.imageUrl != null &&
                        notification.imageUrl!.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Container(
                          constraints: const BoxConstraints(
                            maxHeight: 300, // Tăng chiều cao tối đa
                          ),
                          child: Image.network(
                            notification.imageUrl!,
                            fit: BoxFit
                                .contain, // Thay đổi từ cover sang contain
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                height: 150,
                                color: Colors.grey.withOpacity(0.2),
                                child: const Center(
                                  child: Icon(
                                    Icons.error_outline,
                                    color: Colors.white54,
                                    size: 48,
                                  ),
                                ),
                              );
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Container(
                                height: 150,
                                color: Colors.grey.withOpacity(0.2),
                                child: Center(
                                  child: CircularProgressIndicator(
                                    value: loadingProgress.expectedTotalBytes !=
                                            null
                                        ? loadingProgress
                                                .cumulativeBytesLoaded /
                                            loadingProgress.expectedTotalBytes!
                                        : null,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],

                    const SizedBox(height: 32),

                    // Các nút hành động
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              backgroundColor: Colors.grey.withOpacity(0.2),
                            ),
                            child: Text(
                              'Đóng',
                              style: GoogleFonts.mulish(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),

                        // Nút phản hồi cho thông báo bug (chỉ hiển thị cho admin và developer)
                        if (notification.targetScreen == 'bug_report' &&
                            RoleHelper.hasAdminAccess()) ...[
                          const SizedBox(width: 12),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                _showBugResponseDialog(context, notification);
                              },
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                backgroundColor: Colors.orange,
                              ),
                              child: Text(
                                'Phản hồi',
                                style: GoogleFonts.mulish(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],

                        // Nút xem chi tiết cho các thông báo có targetScreen
                        if (notification.targetScreen != null &&
                            notification.targetScreen != 'bug_report') ...[
                          const SizedBox(width: 12),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                // Điều hướng đến màn hình mục tiêu
                                Get.toNamed(notification.targetScreen!,
                                    parameters: notification.data
                                            as Map<String, String>? ??
                                        {});
                              },
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                backgroundColor: iconColor,
                              ),
                              child: Text(
                                'Xem chi tiết',
                                style: GoogleFonts.mulish(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),

                    // Thêm khoảng trống ở cuối để tránh nút bị che khi bàn phím hiển thị
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
