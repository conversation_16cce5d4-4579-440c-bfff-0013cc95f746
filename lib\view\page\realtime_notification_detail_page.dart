import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../models/realtime_notification_model.dart';
import '../../controllers/realtime_notification_controller.dart';

class RealtimeNotificationDetailPage extends StatelessWidget {
  final RealtimeNotificationModel notification;
  final RealtimeNotificationController _controller = Get.find<RealtimeNotificationController>();

  RealtimeNotificationDetailPage({
    Key? key,
    required this.notification,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chi tiết thông báo',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xff4B79A1), Color(0xff283E51)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () {
              _showDeleteConfirmation(context);
            },
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Tiêu đề
              Text(
                notification.title,
                style: GoogleFonts.mulish(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              
              // Thời gian
              Text(
                'Ngày: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.fromMillisecondsSinceEpoch(notification.createdAt))}',
                style: GoogleFonts.mulish(
                  color: Colors.grey[400],
                ),
              ),
              const SizedBox(height: 16),
              
              // Hình ảnh
              if (notification.imageUrl != null) ...[
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    notification.imageUrl!,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: double.infinity,
                        height: 200,
                        color: Colors.grey[800],
                        child: const Center(
                          child: Icon(Icons.error, size: 50),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),
              ],
              
              // Nội dung
              Text(
                notification.body,
                style: GoogleFonts.mulish(
                  fontSize: 16,
                ),
              ),
              
              // Dữ liệu bổ sung
              if (notification.data != null && notification.data!.isNotEmpty) ...[
                const SizedBox(height: 24),
                const Divider(),
                const SizedBox(height: 16),
                Text(
                  'Thông tin bổ sung:',
                  style: GoogleFonts.mulish(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                _buildAdditionalData(notification.data!),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdditionalData(Map<String, dynamic> data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: data.entries.map((entry) {
        // Bỏ qua các trường đặc biệt
        if (entry.key == 'isAdminNotification') {
          return const SizedBox.shrink();
        }
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${_formatKey(entry.key)}: ',
                style: GoogleFonts.mulish(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Expanded(
                child: Text(
                  _formatValue(entry.value),
                  style: GoogleFonts.mulish(),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  String _formatKey(String key) {
    // Chuyển đổi camelCase thành Title Case
    final formattedKey = key.replaceAllMapped(
      RegExp(r'([A-Z])'),
      (match) => ' ${match.group(0)}',
    );
    
    return formattedKey.substring(0, 1).toUpperCase() + formattedKey.substring(1);
  }

  String _formatValue(dynamic value) {
    if (value == null) return 'N/A';
    
    if (value is Map) {
      return value.entries.map((e) => '${e.key}: ${e.value}').join(', ');
    }
    
    if (value is List) {
      return value.join(', ');
    }
    
    return value.toString();
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Xóa thông báo',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Bạn có chắc chắn muốn xóa thông báo này không?',
          style: GoogleFonts.mulish(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Hủy',
              style: GoogleFonts.mulish(),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteNotification();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text(
              'Xóa',
              style: GoogleFonts.mulish(),
            ),
          ),
        ],
      ),
    );
  }

  void _deleteNotification() async {
    final success = await _controller.deleteNotification(notification.id);
    
    if (success) {
      Get.back();
      Get.snackbar(
        'Thành công',
        'Đã xóa thông báo',
        snackPosition: SnackPosition.BOTTOM,
      );
    } else {
      Get.snackbar(
        'Lỗi',
        'Không thể xóa thông báo: ${_controller.errorMessage}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
