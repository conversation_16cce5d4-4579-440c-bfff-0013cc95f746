import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../../controllers/bug_report_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/bug_report_model.dart';
import '../../utils/role_helper.dart';

class BugReportDetailPage extends StatefulWidget {
  const BugReportDetailPage({Key? key}) : super(key: key);

  @override
  State<BugReportDetailPage> createState() => _BugReportDetailPageState();
}

class _BugReportDetailPageState extends State<BugReportDetailPage> {
  final BugReportController _controller = Get.find<BugReportController>();
  final AuthController _authController = Get.find<AuthController>();
  final TextEditingController _responseController = TextEditingController();
  BugStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    // Sử dụng addPostFrameCallback để đảm bảo widget đã được build xong
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final bugReportId = Get.parameters['bugReportId'];
      if (bugReportId != null) {
        _controller.fetchBugReportDetail(bugReportId);
      }
    });
  }

  @override
  void dispose() {
    _responseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chi tiết báo cáo lỗi',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xff4B79A1), Color(0xff283E51)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: SafeArea(
        child: Obx(() {
          if (_controller.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (_controller.errorMessage.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _controller.errorMessage,
                    style: GoogleFonts.mulish(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      final bugReportId = Get.parameters['bugReportId'];
                      if (bugReportId != null) {
                        _controller.fetchBugReportDetail(bugReportId);
                      }
                    },
                    child: const Text('Thử lại'),
                  ),
                ],
              ),
            );
          }

          final bugReport = _controller.selectedBugReport;
          if (bugReport == null) {
            return const Center(
              child: Text('Không tìm thấy báo cáo lỗi'),
            );
          }

          return _buildBugReportDetail(bugReport);
        }),
      ),
    );
  }

  Widget _buildBugReportDetail(BugReportModel bugReport) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');

    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Thông tin báo cáo
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                bugReport.title,
                                style: GoogleFonts.mulish(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            _buildStatusChip(bugReport.status),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Báo cáo bởi: ${bugReport.reportedByName}',
                          style: GoogleFonts.mulish(
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          'Ngày tạo: ${dateFormat.format(bugReport.createdAt)}',
                          style: GoogleFonts.mulish(
                            color: Colors.grey[600],
                          ),
                        ),
                        const Divider(height: 24),
                        Text(
                          'Mô tả:',
                          style: GoogleFonts.mulish(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          bugReport.description,
                          style: GoogleFonts.mulish(),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Phần cập nhật trạng thái (chỉ cho admin và developer)
                if (RoleHelper.hasAdminAccess())
                  Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Cập nhật trạng thái',
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          DropdownButtonFormField<BugStatus>(
                            value: _selectedStatus,
                            decoration: InputDecoration(
                              labelText: 'Trạng thái mới',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            items: BugStatus.values.map((status) {
                              return DropdownMenuItem<BugStatus>(
                                value: status,
                                child: Text(status.displayName),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedStatus = value;
                              });
                            },
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _selectedStatus == null
                                  ? null
                                  : () => _updateBugStatus(bugReport.id),
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                backgroundColor: Colors.blue,
                              ),
                              child: Text(
                                'Cập nhật trạng thái',
                                style: GoogleFonts.mulish(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                const SizedBox(height: 24),

                // Danh sách phản hồi
                Text(
                  'Phản hồi (${bugReport.responses.length})',
                  style: GoogleFonts.mulish(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),

                if (bugReport.responses.isEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Center(
                      child: Text(
                        'Chưa có phản hồi nào',
                        style: GoogleFonts.mulish(
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  )
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: bugReport.responses.length,
                    itemBuilder: (context, index) {
                      return _buildResponseItem(bugReport.responses[index]);
                    },
                  ),
              ],
            ),
          ),
        ),

        // Phần nhập phản hồi
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, -3),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _responseController,
                  decoration: InputDecoration(
                    hintText: 'Nhập phản hồi của bạn...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  maxLines: 3,
                  minLines: 1,
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: () => _sendResponse(bugReport.id),
                icon: const Icon(Icons.send),
                color: Colors.blue,
                iconSize: 28,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildResponseItem(BugResponseModel response) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    final isCurrentUser = response.responderId == _authController.user?.id;

    // Xác định màu sắc dựa trên người gửi
    Color backgroundColor;
    if (response.responderId == 'system') {
      backgroundColor = Colors.grey[200]!;
    } else if (response.isFromDeveloper) {
      backgroundColor = Colors.blue[100]!;
    } else if (response.isFromAdmin) {
      backgroundColor = Colors.green[100]!;
    } else {
      backgroundColor = isCurrentUser ? Colors.blue[50]! : Colors.grey[100]!;
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment:
            isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
            ),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      response.responderName,
                      style: GoogleFonts.mulish(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (response.isFromDeveloper)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Developer',
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      )
                    else if (response.isFromAdmin)
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Admin',
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  response.message,
                  style: GoogleFonts.mulish(),
                ),
                const SizedBox(height: 4),
                Text(
                  dateFormat.format(response.createdAt),
                  style: GoogleFonts.mulish(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                if (response.newStatus != null) ...[
                  const SizedBox(height: 8),
                  _buildStatusChip(response.newStatus!),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(BugStatus status) {
    Color color;
    switch (status) {
      case BugStatus.pending:
        color = Colors.grey;
        break;
      case BugStatus.accepted:
        color = Colors.blue;
        break;
      case BugStatus.inProgress:
        color = Colors.orange;
        break;
      case BugStatus.fixed:
        color = Colors.green;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color),
      ),
      child: Text(
        status.displayName,
        style: GoogleFonts.mulish(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  void _sendResponse(String bugReportId) async {
    if (_responseController.text.trim().isEmpty) {
      return;
    }

    final success = await _controller.addResponse(
      bugReportId: bugReportId,
      message: _responseController.text.trim(),
    );

    if (success) {
      _responseController.clear();
    }
  }

  void _updateBugStatus(String bugReportId) async {
    if (_selectedStatus == null) {
      return;
    }

    final success = await _controller.updateBugStatus(
      bugReportId: bugReportId,
      newStatus: _selectedStatus!,
    );

    if (success) {
      setState(() {
        _selectedStatus = null;
      });
    }
  }
}
