# Hướng dẫn Import hàng loạt

Ứng dụng Đớp Phim hỗ trợ import hàng loạt dữ liệu phim, rạp chiếu và phòng chiếu từ file Excel, CSV hoặc JSON.

## Cách sử dụng

### 1. <PERSON><PERSON><PERSON> cập chức năng Import
- Đăng nhập với tài khoản Admin hoặc Developer
- Vào trang quản lý tương ứng:
  - **Quản lý Phim**: Nhấn nút upload (📤) để import phim
  - **Quản lý Rạp**: Nhấn nút upload (📤) để import rạp chiếu
  - **Quản lý Phòng**: Nhấn nút upload (📤) để import phòng chiếu

### 2. Chọn file
- Hỗ trợ các định dạng: `.xlsx`, `.xls`, `.csv`, `.json`
- <PERSON>hấn vào vùng upload để chọn file từ máy tính
- File sẽ được phân tích và hiển thị preview

### 3. Kiểm tra dữ liệu
- Xem trước 5 dòng đầu tiên của dữ liệu
- Kiểm tra các trường bắt buộc và tùy chọn
- Nhấn nút "ℹ️" để xem hướng dẫn chi tiết

### 4. Thực hiện Import
- Nhấn "Bắt đầu Import" để bắt đầu quá trình
- Xem kết quả import với thống kê thành công/lỗi
- Nếu có lỗi, xem chi tiết để sửa file và import lại

## Cấu trúc dữ liệu

### Import Phim (Movies)

#### Trường bắt buộc:
- `title`: Tên phim
- `genres`: Thể loại (phân cách bằng dấu phẩy)
- `status`: Trạng thái (`now_playing`, `upcoming`, `ended`)

#### Trường tùy chọn:
- `originalTitle`: Tên gốc
- `subtitle`: Phụ đề
- `overview`: Mô tả phim
- `posterPath`: URL poster
- `backdropPath`: URL backdrop
- `trailerUrl`: URL trailer
- `releaseDate`: Ngày phát hành (YYYY-MM-DD)
- `runtime`: Thời lượng (phút)
- `voteAverage`: Điểm đánh giá (0-10)
- `voteCount`: Số lượt đánh giá
- `popularity`: Độ phổ biến
- `ageRating`: Phân loại độ tuổi
- `language`: Ngôn ngữ
- `country`: Quốc gia
- `director`: Đạo diễn
- `cast`: Diễn viên (JSON format)
- `isActive`: Hoạt động (true/false)
- `isHomeBanner`: Banner trang chủ (true/false)
- `isSplashBanner`: Banner khởi động (true/false)
- `bannerOrder`: Thứ tự banner (số)

### Import Rạp chiếu (Theaters)

#### Trường bắt buộc:
- `name`: Tên rạp
- `address_street`: Địa chỉ đường
- `address_city`: Thành phố
- `phoneNumber`: Số điện thoại

#### Trường tùy chọn:
- `address_district`: Quận/Huyện
- `address_province`: Tỉnh/Thành phố
- `latitude`: Vĩ độ
- `longitude`: Kinh độ
- `email`: Email
- `facilities`: Tiện ích (phân cách bằng dấu phẩy)
- `operatingHours`: Giờ hoạt động (JSON format)
- `isActive`: Hoạt động (true/false)

### Import Phòng chiếu (Screens)

#### Trường bắt buộc:
- `theaterId`: ID rạp chiếu (tự động điền nếu import từ trang quản lý rạp)
- `name`: Tên phòng chiếu
- `type`: Loại phòng (`standard`, `premium`, `imax`, `vip`, `dolby`)
- `totalSeats`: Tổng số ghế
- `rows`: Số hàng ghế
- `seatsPerRow`: Số ghế mỗi hàng

#### Trường tùy chọn:
- `amenities`: Tiện nghi (phân cách bằng dấu phẩy)
- `isActive`: Hoạt động (true/false)

## File mẫu

Trong thư mục `assets/` có các file mẫu:
- `sample_movies.json`: Mẫu import phim
- `sample_theaters.json`: Mẫu import rạp chiếu

## Kiểm tra trùng lặp

Hệ thống tự động kiểm tra trùng lặp khi import:

### **Phim (Movies):**
- **Trùng lặp nếu**: Cùng tên phim (không phân biệt hoa thường) và cùng năm phát hành
- **Kiểm tra**: So với database hiện tại và trong file import

### **Rạp chiếu (Theaters):**
- **Trùng lặp nếu**: Cùng tên rạp và cùng thành phố
- **Kiểm tra**: So với database hiện tại và trong file import

### **Phòng chiếu (Screens):**
- **Trùng lặp nếu**: Cùng tên phòng trong cùng rạp chiếu
- **Kiểm tra**: So với database hiện tại và trong file import

## Lưu ý quan trọng

1. **Định dạng dữ liệu**: Đảm bảo dữ liệu đúng định dạng, đặc biệt:
   - Ngày tháng: YYYY-MM-DD
   - Boolean: true/false
   - Số: không có ký tự đặc biệt

2. **Trường bắt buộc**: Tất cả trường bắt buộc phải có giá trị

3. **ID rạp chiếu**:
   - Khi import từ **trang quản lý rạp**: theaterId sẽ tự động điền nếu để trống
   - Nếu có theaterId trong file nhưng không trùng với rạp hiện tại: sẽ báo lỗi
   - Khi import từ **trang quản lý chung**: cần có theaterId hợp lệ

4. **Kiểm tra trùng lặp**: Hệ thống sẽ từ chối import các mục trùng lặp và hiển thị lỗi chi tiết

5. **Xử lý lỗi**: Nếu có lỗi, sửa file và import lại. Hệ thống sẽ bỏ qua các dòng lỗi

6. **Backup**: Nên backup dữ liệu trước khi import hàng loạt

## Hỗ trợ

Nếu gặp vấn đề khi import, vui lòng:
1. Kiểm tra định dạng file và dữ liệu
2. Xem chi tiết lỗi trong kết quả import
3. Tham khảo file mẫu
4. Liên hệ admin để được hỗ trợ
