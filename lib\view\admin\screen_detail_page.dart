import 'package:flutter/material.dart';
import 'package:get/get.dart' hide ScreenType;
import 'package:google_fonts/google_fonts.dart';
import '../../models/screen_model.dart';
import '../../controllers/screen_controller.dart';
import 'add_edit_screen_page.dart';

class ScreenDetailPage extends StatelessWidget {
  final ScreenModel screen;

  const ScreenDetailPage({Key? key, required this.screen}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ScreenController screenController = Get.find<ScreenController>();

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xff2B5876), Color(0xff4E4376)],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        screen.name,
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    PopupMenuButton<String>(
                      icon: const Icon(Icons.more_vert, color: Colors.white),
                      color: const Color(0xff2B5876),
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            Get.to(() => AddEditScreenPage(screen: screen));
                            break;
                          case 'toggle':
                            _showToggleStatusDialog(context, screenController);
                            break;
                          case 'delete':
                            _showDeleteDialog(context, screenController);
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              const Icon(Icons.edit, color: Colors.white),
                              const SizedBox(width: 8),
                              Text(
                                'Chỉnh sửa',
                                style: GoogleFonts.mulish(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'toggle',
                          child: Row(
                            children: [
                              Icon(
                                screen.isActive ? Icons.pause : Icons.play_arrow,
                                color: screen.isActive ? Colors.orange : Colors.green,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                screen.isActive ? 'Tạm dừng' : 'Kích hoạt',
                                style: GoogleFonts.mulish(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              const Icon(Icons.delete, color: Colors.red),
                              const SizedBox(width: 8),
                              Text(
                                'Xóa',
                                style: GoogleFonts.mulish(color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      _buildInfoCard(),
                      const SizedBox(height: 16),
                      _buildSeatingCard(),
                      const SizedBox(height: 16),
                      _buildAmenitiesCard(),
                      const SizedBox(height: 16),
                      _buildStatusCard(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    final theater = Get.find<ScreenController>().getTheaterById(screen.theaterId);
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(screen.type.icon, color: screen.type.color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Thông tin cơ bản',
                  style: GoogleFonts.mulish(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('Rạp chiếu', theater?.name ?? 'Unknown Theater'),
          _buildInfoRow('Loại phòng', screen.type.displayName),
          _buildInfoRow('Tổng số ghế', '${screen.totalSeats} ghế'),
          _buildInfoRow('Cấu hình', '${screen.rows} hàng × ${screen.seatsPerRow} ghế'),
          _buildInfoRow('Ngày tạo', _formatDate(screen.createdAt)),
          _buildInfoRow('Cập nhật', _formatDate(screen.updatedAt)),
        ],
      ),
    );
  }

  Widget _buildSeatingCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.event_seat, color: Colors.blue, size: 24),
              const SizedBox(width: 12),
              Text(
                'Sơ đồ ghế ngồi',
                style: GoogleFonts.mulish(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Screen indicator
          Container(
            width: double.infinity,
            height: 8,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 4),
          Center(
            child: Text(
              'MÀN HÌNH',
              style: GoogleFonts.mulish(
                fontSize: 12,
                color: Colors.white.withOpacity(0.7),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Seat layout
          ...screen.seatLayout.map((row) => _buildSeatRow(row)).toList(),
          
          const SizedBox(height: 16),
          
          // Legend
          _buildSeatLegend(),
        ],
      ),
    );
  }

  Widget _buildSeatRow(SeatRowModel row) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            child: Text(
              row.row,
              style: GoogleFonts.mulish(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: row.seats.map((seat) => _buildSeat(seat)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSeat(SeatModel seat) {
    Color seatColor;
    switch (seat.type) {
      case SeatType.vip:
        seatColor = Colors.amber;
        break;
      case SeatType.couple:
        seatColor = Colors.pink;
        break;
      case SeatType.disabled:
        seatColor = Colors.blue;
        break;
      default:
        seatColor = Colors.grey;
    }

    return Container(
      width: 16,
      height: 16,
      margin: const EdgeInsets.all(1),
      decoration: BoxDecoration(
        color: seatColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildSeatLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildLegendItem(Colors.grey, 'Thường'),
        _buildLegendItem(Colors.amber, 'VIP'),
        _buildLegendItem(Colors.pink, 'Đôi'),
        _buildLegendItem(Colors.blue, 'Khuyết tật'),
      ],
    );
  }

  Widget _buildLegendItem(Color color, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: GoogleFonts.mulish(
            color: Colors.white.withOpacity(0.8),
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildAmenitiesCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.star, color: Colors.amber, size: 24),
              const SizedBox(width: 12),
              Text(
                'Tiện ích',
                style: GoogleFonts.mulish(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (screen.amenities.isEmpty)
            Text(
              'Chưa có tiện ích nào',
              style: GoogleFonts.mulish(
                color: Colors.white.withOpacity(0.7),
                fontStyle: FontStyle.italic,
              ),
            )
          else
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: screen.amenities.map((amenity) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.blue.withOpacity(0.5)),
                  ),
                  child: Text(
                    amenity,
                    style: GoogleFonts.mulish(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                screen.isActive ? Icons.check_circle : Icons.pause_circle,
                color: screen.isActive ? Colors.green : Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Trạng thái',
                style: GoogleFonts.mulish(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: (screen.isActive ? Colors.green : Colors.orange).withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: (screen.isActive ? Colors.green : Colors.orange).withOpacity(0.5),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  screen.isActive ? Icons.play_circle : Icons.pause_circle,
                  color: screen.isActive ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        screen.isActive ? 'Đang hoạt động' : 'Tạm dừng hoạt động',
                        style: GoogleFonts.mulish(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        screen.isActive 
                            ? 'Phòng chiếu có thể được sử dụng để tạo lịch chiếu'
                            : 'Phòng chiếu sẽ không hiển thị trong danh sách lịch chiếu',
                        style: GoogleFonts.mulish(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: GoogleFonts.mulish(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.mulish(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showToggleStatusDialog(BuildContext context, ScreenController controller) {
    final action = screen.isActive ? 'tạm dừng' : 'kích hoạt';
    
    Get.dialog(
      AlertDialog(
        backgroundColor: const Color(0xff2B5876),
        title: Text(
          'Xác nhận',
          style: GoogleFonts.mulish(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        content: Text(
          'Bạn có chắc chắn muốn $action phòng chiếu "${screen.name}"?',
          style: GoogleFonts.mulish(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Hủy',
              style: GoogleFonts.mulish(color: Colors.white.withOpacity(0.7)),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              final success = await controller.toggleScreenStatus(screen);
              if (success) {
                Get.back(); // Go back to previous screen
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: screen.isActive ? Colors.orange : Colors.green,
            ),
            child: Text(
              action.toUpperCase(),
              style: GoogleFonts.mulish(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, ScreenController controller) {
    Get.dialog(
      AlertDialog(
        backgroundColor: const Color(0xff2B5876),
        title: Text(
          'Xác nhận xóa',
          style: GoogleFonts.mulish(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        content: Text(
          'Bạn có chắc chắn muốn xóa phòng chiếu "${screen.name}"?\n\nHành động này không thể hoàn tác.',
          style: GoogleFonts.mulish(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'Hủy',
              style: GoogleFonts.mulish(color: Colors.white.withOpacity(0.7)),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              final success = await controller.deleteScreen(screen);
              if (success) {
                Get.back(); // Go back to previous screen
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text(
              'XÓA',
              style: GoogleFonts.mulish(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
