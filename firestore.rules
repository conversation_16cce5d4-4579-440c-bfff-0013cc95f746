rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to check if user is developer
    function isDeveloper() {
      return isAuthenticated() &&
        exists(/databases/$(database)/documents/user_roles/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/user_roles/$(request.auth.uid)).data.role == 'developer';
    }

    // Helper function to check if user is admin
    function isAdmin() {
      return isAuthenticated() && (
        // Check if user is developer (developers have admin rights)
        isDeveloper() ||
        // Check if user has admin role
        (exists(/databases/$(database)/documents/user_roles/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/user_roles/$(request.auth.uid)).data.role == 'admin')
      );
    }

    // Helper function to check if user is accessing their own data
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Helper function to check if user is active
    function isActiveUser() {
      return isAuthenticated() &&
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true;
    }

    // Users collection
    match /users/{userId} {
      // Anyone can read user profiles
      // Only the user or an admin can write to their profile
      allow read: if isAuthenticated();
      allow create: if isOwner(userId);
      allow update: if isOwner(userId) || isAdmin();
      allow delete: if isAdmin();
    }

    // User roles collection
    match /user_roles/{userId} {
      // Only admins can read and write user roles
      // Exception: users can read their own role
      allow read: if isOwner(userId) || isAdmin();
      allow write: if isAdmin();
    }



    // Banners collection
    match /banners/{bannerId} {
      // Anyone can read banners
      // Only admins can write to banners
      allow read: if true;
      allow write: if isAdmin();
    }

    // Notifications collection
    match /notifications/{notificationId} {
      // Anyone can read notifications
      // Only admins can write to notifications
      allow read: if true;
      allow write: if isAdmin();
    }

    // User notifications collection
    match /user_notifications/{userNotificationId} {
      // Người dùng chỉ có thể đọc trạng thái thông báo của chính họ
      allow read: if isAuthenticated() &&
        resource.data.userId == request.auth.uid;

      // Người dùng chỉ có thể tạo và cập nhật trạng thái thông báo của chính họ
      allow create, update: if isAuthenticated() &&
        request.resource.data.userId == request.auth.uid;

      // Chỉ admin và developer có thể xóa trạng thái thông báo
      allow delete: if isAdmin() || isDeveloper();
    }

    // Movies collection
    match /movies/{movieId} {
      // Anyone can read movies
      // Only admins can write to movies
      allow read: if true;
      allow write: if isAdmin();
    }

    // Favorites collection
    match /favorites/{favoriteId} {
      // Users can read and write their own favorites
      // Admins can read all favorites
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow write: if isOwner(resource.data.userId);
    }

    // Tickets collection
    match /tickets/{ticketId} {
      // Users can read and write their own tickets
      // Admins can read all tickets
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow write: if isOwner(resource.data.userId) || isAdmin();
    }

    // Theaters collection
    match /theaters/{theaterId} {
      allow read: if true; // Public read
      allow write: if isAdmin();
    }

    // Screens collection
    match /screens/{screenId} {
      allow read: if true; // Public read
      allow write: if isAdmin();
    }

    // Showtimes collection
    match /showtimes/{showtimeId} {
      allow read: if true; // Public read
      allow create: if isAdmin();
      allow update: if isActiveUser() || isAdmin(); // Users can book seats
      allow delete: if isAdmin();
    }

    // Payments collection
    match /payments/{paymentId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isActiveUser() && isOwner(request.resource.data.userId);
      allow update: if isAdmin(); // Only admin can update payment status
      allow delete: if isAdmin();
    }

    // Reviews collection
    match /reviews/{reviewId} {
      allow read: if resource.data.status == 'active' || isOwner(resource.data.userId) || isAdmin();
      allow create: if isActiveUser() && isOwner(request.resource.data.userId);
      allow update: if isOwner(resource.data.userId) || isAdmin();
      allow delete: if isOwner(resource.data.userId) || isAdmin();
    }

    // Promotions collection
    match /promotions/{promotionId} {
      allow read: if resource.data.isActive == true || isAdmin();
      allow write: if isAdmin();
    }

    // User promotions collection
    match /user_promotions/{userPromotionId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isActiveUser() && isOwner(request.resource.data.userId);
      allow update, delete: if isAdmin();
    }

    // Loyalty transactions collection
    match /loyalty_transactions/{transactionId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isActiveUser() && isOwner(request.resource.data.userId);
      allow update, delete: if isAdmin();
    }

    // Bug reports collection
    match /bug_reports/{bugReportId} {
      // Người dùng có thể đọc báo cáo lỗi của họ
      // Admin và developer có thể đọc tất cả báo cáo lỗi
      allow read: if isAuthenticated() &&
        (resource.data.reportedBy == request.auth.uid || isAdmin() || isDeveloper());

      // Người dùng có thể tạo báo cáo lỗi
      allow create: if isAuthenticated() &&
        request.resource.data.reportedBy == request.auth.uid;

      // Chỉ admin và developer có thể cập nhật báo cáo lỗi
      allow update: if isAdmin() || isDeveloper();

      // Chỉ admin có thể xóa báo cáo lỗi
      allow delete: if isAdmin();
    }

    // Default deny all
    match /{document=**} {
      allow read, write: if false;
    }
  }
}