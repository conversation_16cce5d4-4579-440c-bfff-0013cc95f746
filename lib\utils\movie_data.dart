
class MovieData {
  
  static List<String> getSplashBanners() {
    return [
      'https://image.tmdb.org/t/p/original/uGBVj3bEbCoZbDjjl9wTxcygko1.jpg', // <PERSON>
      'https://image.tmdb.org/t/p/original/7RyHsO4yDXtBv1zUU3mTpHeQ0d5.jpg', // Avengers Endgame
      'https://image.tmdb.org/t/p/original/t6HIqrRAclMCA60NsSmeqe9RmNV.jpg', // Avatar 2
      'https://image.tmdb.org/t/p/original/jPEXVFKQY2oPUJ5VwmRVF6ESrOy.jpg', // KGF 2
      'https://image.tmdb.org/t/p/original/wp6OxE4poJ4G7c0U2ZIXasTSMR7.jpg', // Thor: The Dark World
    ];
  }

  /// Get a list of movie titles to display on the splash screen
  static List<String> getMovieTitles() {
    return [
      'Doctor Strange',
      'Avengers: Endgame',
      'Avatar: The Way of Water',
      'KGF Chapter 2',
      'Thor: The Dark World',
    ];
  }
}
