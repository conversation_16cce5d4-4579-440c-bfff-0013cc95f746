import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/notification_model.dart';

class NotificationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection references
  final CollectionReference _notificationsCollection;
  final CollectionReference _userNotificationsCollection;

  NotificationService()
      : _notificationsCollection =
            FirebaseFirestore.instance.collection('notifications'),
        _userNotificationsCollection =
            FirebaseFirestore.instance.collection('user_notifications');

  // Lấy tất cả thông báo công khai
  Future<List<NotificationModel>> getPublicNotifications() async {
    try {
      print('NotificationService: Getting public notifications');

      try {
        // Kiểm tra xem collection có tồn tại không
        final collectionExists =
            await _firestore.collection('notifications').limit(1).get();
        print(
            'NotificationService: Collection exists: ${collectionExists.docs.isNotEmpty}');

        // L<PERSON><PERSON> tất cả thông báo công khai
        final querySnapshot = await _notificationsCollection.get();

        print(
            'NotificationService: Found ${querySnapshot.docs.length} notifications');

        // Kiểm tra cấu trúc dữ liệu
        if (querySnapshot.docs.isNotEmpty) {
          final firstDoc = querySnapshot.docs.first;
          print(
              'NotificationService: First notification data: ${firstDoc.data()}');
        }

        // Chuyển đổi thành NotificationModel
        final notifications = querySnapshot.docs
            .map((doc) {
              try {
                return NotificationModel.fromFirestore(doc);
              } catch (e) {
                print(
                    'NotificationService: Error converting doc ${doc.id} to NotificationModel: $e');
                return null;
              }
            })
            .whereType<NotificationModel>()
            .toList();

        print(
            'NotificationService: Converted ${notifications.length} notifications');

        return notifications;
      } catch (firestoreError) {
        print(
            'NotificationService: Error accessing Firestore: $firestoreError');

        // Nếu không thể truy cập Firestore, trả về danh sách thông báo mẫu
        return _getSampleNotifications();
      }
    } catch (e) {
      print('NotificationService: Error getting public notifications: $e');
      return [];
    }
  }

  // Tạo danh sách thông báo mẫu khi không thể truy cập Firestore
  List<NotificationModel> _getSampleNotifications() {
    print('NotificationService: Returning sample notifications');

    return [
      NotificationModel(
        id: 'sample1',
        title: 'Chào mừng đến với Đớp Phim',
        body:
            'Cảm ơn bạn đã sử dụng ứng dụng Đớp Phim. Chúc bạn có trải nghiệm tuyệt vời!',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        expiresAt: DateTime.now().add(const Duration(days: 30)),
        isPublic: true,
      ),
      NotificationModel(
        id: 'sample2',
        title: 'Lưu ý về quyền truy cập',
        body:
            'Ứng dụng đang hoạt động ở chế độ offline do vấn đề quyền truy cập Firestore. Một số tính năng có thể bị hạn chế.',
        createdAt: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(days: 30)),
        isPublic: true,
      ),
    ];
  }

  Future<List<NotificationModel>> getUserTargetedNotifications(
      String userId) async {
    try {
      try {
        final querySnapshot = await _notificationsCollection
            .where('isPublic', isEqualTo: false)
            .where('targetUserIds', arrayContains: userId)
            .where('expiresAt', isGreaterThan: Timestamp.now())
            .orderBy('expiresAt', descending: true)
            .orderBy('createdAt', descending: true)
            .get();

        return querySnapshot.docs
            .map((doc) => NotificationModel.fromFirestore(doc))
            .toList();
      } catch (firestoreError) {
        print(
            'Error accessing Firestore for targeted notifications: $firestoreError');

        // Nếu không thể truy cập Firestore, trả về danh sách trống
        // Hoặc có thể trả về thông báo mẫu dành riêng cho người dùng
        return _getUserSampleNotifications(userId);
      }
    } catch (e) {
      print('Error getting user targeted notifications: $e');
      return [];
    }
  }

  // Tạo danh sách thông báo mẫu dành riêng cho người dùng
  List<NotificationModel> _getUserSampleNotifications(String userId) {
    print('NotificationService: Returning sample user notifications');

    return [
      NotificationModel(
        id: 'user-sample1',
        title: 'Thông báo riêng tư',
        body:
            'Đây là thông báo riêng tư dành cho bạn. Ứng dụng đang hoạt động ở chế độ offline.',
        createdAt: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(days: 30)),
        isPublic: false,
        targetUserIds: [userId],
      ),
    ];
  }

  Future<List<UserNotificationModel>> getUserNotificationStates(
      String userId) async {
    try {
      try {
        final querySnapshot = await _userNotificationsCollection
            .where('userId', isEqualTo: userId)
            .where('isDeleted', isEqualTo: false)
            .get();

        return querySnapshot.docs
            .map((doc) => UserNotificationModel.fromFirestore(doc))
            .toList();
      } catch (firestoreError) {
        print(
            'Error accessing Firestore for user notification states: $firestoreError');

        // Nếu không thể truy cập Firestore, trả về danh sách trống
        // Hoặc có thể tạo trạng thái mẫu cho các thông báo mẫu
        return _getSampleUserNotificationStates(userId);
      }
    } catch (e) {
      print('Error getting user notification states: $e');
      return [];
    }
  }

  // Tạo danh sách trạng thái thông báo mẫu
  List<UserNotificationModel> _getSampleUserNotificationStates(String userId) {
    print('NotificationService: Returning sample user notification states');

    // Tạo trạng thái cho các thông báo mẫu
    return [
      UserNotificationModel(
        id: 'state-sample1',
        userId: userId,
        notificationId: 'sample1',
        isRead: false,
        isSeen: true,
        isDeleted: false,
        createdAt: DateTime.now(),
        seenAt: DateTime.now(),
      ),
      UserNotificationModel(
        id: 'state-sample2',
        userId: userId,
        notificationId: 'sample2',
        isRead: false,
        isSeen: false,
        isDeleted: false,
        createdAt: DateTime.now(),
      ),
      UserNotificationModel(
        id: 'state-user-sample1',
        userId: userId,
        notificationId: 'user-sample1',
        isRead: false,
        isSeen: false,
        isDeleted: false,
        createdAt: DateTime.now(),
      ),
    ];
  }

  Future<UserNotificationModel?> createOrUpdateUserNotification({
    required String userId,
    required String notificationId,
    bool? isRead,
    bool? isSeen,
    bool? isDeleted,
  }) async {
    try {
      final now = DateTime.now();

      try {
        final querySnapshot = await _userNotificationsCollection
            .where('userId', isEqualTo: userId)
            .where('notificationId', isEqualTo: notificationId)
            .limit(1)
            .get();

        if (querySnapshot.docs.isEmpty) {
          final newUserNotification = UserNotificationModel(
            id: '',
            userId: userId,
            notificationId: notificationId,
            isRead: isRead ?? false,
            isSeen: isSeen ?? false,
            isDeleted: isDeleted ?? false,
            createdAt: now,
            readAt: isRead == true ? now : null,
            seenAt: isSeen == true ? now : null,
            deletedAt: isDeleted == true ? now : null,
          );

          try {
            final docRef = await _userNotificationsCollection
                .add(newUserNotification.toFirestore());
            return newUserNotification.copyWith(id: docRef.id);
          } catch (writeError) {
            print('Error writing new user notification: $writeError');
            // Trả về đối tượng với ID giả
            return newUserNotification.copyWith(
                id: 'local-${DateTime.now().millisecondsSinceEpoch}');
          }
        } else {
          final doc = querySnapshot.docs.first;
          final existingUserNotification =
              UserNotificationModel.fromFirestore(doc);

          final updatedUserNotification = existingUserNotification.copyWith(
            isRead: isRead ?? existingUserNotification.isRead,
            isSeen: isSeen ?? existingUserNotification.isSeen,
            isDeleted: isDeleted ?? existingUserNotification.isDeleted,
            readAt: isRead == true && !existingUserNotification.isRead
                ? now
                : existingUserNotification.readAt,
            seenAt: isSeen == true && !existingUserNotification.isSeen
                ? now
                : existingUserNotification.seenAt,
            deletedAt: isDeleted == true && !existingUserNotification.isDeleted
                ? now
                : existingUserNotification.deletedAt,
          );

          try {
            await _userNotificationsCollection
                .doc(doc.id)
                .update(updatedUserNotification.toFirestore());
          } catch (updateError) {
            print('Error updating user notification: $updateError');
            // Bỏ qua lỗi cập nhật, vẫn trả về đối tượng đã cập nhật
          }

          return updatedUserNotification;
        }
      } catch (firestoreError) {
        print(
            'Error accessing Firestore for user notification: $firestoreError');

        // Tạo một đối tượng UserNotificationModel mới với ID giả
        final localUserNotification = UserNotificationModel(
          id: 'local-${DateTime.now().millisecondsSinceEpoch}',
          userId: userId,
          notificationId: notificationId,
          isRead: isRead ?? false,
          isSeen: isSeen ?? false,
          isDeleted: isDeleted ?? false,
          createdAt: now,
          readAt: isRead == true ? now : null,
          seenAt: isSeen == true ? now : null,
          deletedAt: isDeleted == true ? now : null,
        );

        return localUserNotification;
      }
    } catch (e) {
      print('Error creating/updating user notification: $e');
      return null;
    }
  }

  Future<bool> markAllAsRead(String userId) async {
    try {
      try {
        final batch = _firestore.batch();

        final querySnapshot = await _userNotificationsCollection
            .where('userId', isEqualTo: userId)
            .where('isRead', isEqualTo: false)
            .where('isDeleted', isEqualTo: false)
            .get();

        if (querySnapshot.docs.isEmpty) {
          return true;
        }

        final now = Timestamp.now();

        for (final doc in querySnapshot.docs) {
          batch.update(doc.reference, {
            'isRead': true,
            'isSeen': true,
            'readAt': now,
            'seenAt': now,
          });
        }

        await batch.commit();
        return true;
      } catch (firestoreError) {
        print(
            'Error accessing Firestore for marking all as read: $firestoreError');

        // Nếu không thể truy cập Firestore, vẫn trả về true để ứng dụng tiếp tục hoạt động
        // Trong trường hợp thực tế, có thể lưu trạng thái này vào bộ nhớ cục bộ
        return true;
      }
    } catch (e) {
      print('Error marking all notifications as read: $e');
      return false;
    }
  }

  Future<bool> markAllAsSeen(String userId) async {
    try {
      try {
        final batch = _firestore.batch();

        final querySnapshot = await _userNotificationsCollection
            .where('userId', isEqualTo: userId)
            .where('isSeen', isEqualTo: false)
            .where('isDeleted', isEqualTo: false)
            .get();

        if (querySnapshot.docs.isEmpty) {
          return true;
        }

        final now = Timestamp.now();

        for (final doc in querySnapshot.docs) {
          batch.update(doc.reference, {
            'isSeen': true,
            'seenAt': now,
          });
        }

        await batch.commit();
        return true;
      } catch (firestoreError) {
        print(
            'Error accessing Firestore for marking all as seen: $firestoreError');

        // Nếu không thể truy cập Firestore, vẫn trả về true để ứng dụng tiếp tục hoạt động
        // Trong trường hợp thực tế, có thể lưu trạng thái này vào bộ nhớ cục bộ
        return true;
      }
    } catch (e) {
      print('Error marking all notifications as seen: $e');
      return false;
    }
  }

  Future<NotificationModel?> createNotification({
    required String title,
    required String body,
    String? imageUrl,
    DateTime? expiresAt,
    String? targetScreen,
    Map<String, dynamic>? data, // Đảm bảo kiểu dữ liệu là Map<String, dynamic>?
    bool isPublic = true,
    List<String>? targetUserIds,
  }) async {
    try {
      print('NotificationService: Creating notification: $title');

      // Kiểm tra xem thông báo tương tự đã tồn tại chưa
      if (targetScreen == 'bug_report' || targetScreen == 'bug_report_detail') {
        try {
          // Nếu là thông báo liên quan đến báo cáo lỗi, kiểm tra trùng lặp
          final bugReportId = data?['bugReportId'] as String?;

          if (bugReportId != null) {
            // Tìm kiếm thông báo có cùng bugReportId
            final querySnapshot = await _notificationsCollection
                .where('targetScreen', isEqualTo: targetScreen)
                .where('data.bugReportId', isEqualTo: bugReportId)
                .limit(1)
                .get();

            if (querySnapshot.docs.isNotEmpty) {
              print(
                  'NotificationService: Similar bug report notification already exists, skipping');

              // Trả về thông báo hiện có
              final existingDoc = querySnapshot.docs.first;
              return NotificationModel.fromFirestore(existingDoc);
            }
          }
        } catch (e) {
          print(
              'NotificationService: Error checking for duplicate bug notifications: $e');
          // Tiếp tục tạo thông báo mới nếu có lỗi khi kiểm tra
        }
      }

      final notification = NotificationModel(
        id: '',
        title: title,
        body: body,
        imageUrl: imageUrl,
        createdAt: DateTime.now(),
        expiresAt: expiresAt ?? DateTime.now().add(const Duration(days: 30)),
        targetScreen: targetScreen,
        data: data,
        isPublic: isPublic,
        targetUserIds: targetUserIds,
      );

      try {
        final docRef =
            await _notificationsCollection.add(notification.toFirestore());
        print(
            'NotificationService: Created notification with ID: ${docRef.id}');
        return notification.copyWith(id: docRef.id);
      } catch (firestoreError) {
        print(
            'NotificationService: Error writing to Firestore: $firestoreError');

        // Nếu không thể ghi vào Firestore, lưu vào bộ nhớ cục bộ
        // Đây là giải pháp tạm thời để đảm bảo ứng dụng vẫn hoạt động
        final id = DateTime.now().millisecondsSinceEpoch.toString();

        // Lưu vào bộ nhớ cục bộ (có thể sử dụng SharedPreferences hoặc Hive)
        // Ở đây chúng ta chỉ trả về thông báo với ID giả
        print('NotificationService: Created local notification with ID: $id');
        return notification.copyWith(id: id);
      }
    } catch (e) {
      print('NotificationService: Error creating notification: $e');
      return null;
    }
  }

  // Tạo thông báo mẫu để kiểm tra
  Future<void> createSampleNotifications() async {
    try {
      print('NotificationService: Creating sample notifications');

      // Tạo thông báo mẫu
      await createNotification(
        title: 'Chào mừng đến với Đớp Phim',
        body:
            'Cảm ơn bạn đã sử dụng ứng dụng Đớp Phim. Chúc bạn có trải nghiệm tuyệt vời!',
        imageUrl:
            'https://images.unsplash.com/photo-1536440136628-849c177e76a1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1025&q=80',
        targetScreen: null,
        isPublic: true,
      );

      await createNotification(
        title: 'Phim mới: Fast & Furious 10',
        body:
            'Fast & Furious 10 sẽ được công chiếu vào ngày 15/07. Đặt vé ngay để nhận ưu đãi!',
        imageUrl:
            'https://images.unsplash.com/photo-1594909122845-11baa439b7bf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
        targetScreen: 'movie_detail',
        data: {'movieId': '1'},
        isPublic: true,
      );

      await createNotification(
        title: 'Khuyến mãi đặc biệt',
        body: 'Giảm 50% cho vé xem phim vào thứ 4 hàng tuần. Đặt vé ngay!',
        imageUrl:
            'https://images.unsplash.com/photo-1586899028174-e7098604235b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1171&q=80',
        targetScreen: 'promo',
        isPublic: true,
      );

      print('NotificationService: Sample notifications created successfully');
    } catch (e) {
      print('NotificationService: Error creating sample notifications: $e');
    }
  }
}

extension NotificationModelExtension on NotificationModel {
  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? expiresAt,
    String? targetScreen,
    Map<String, dynamic>? data,
    bool? isPublic,
    List<String>? targetUserIds,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      targetScreen: targetScreen ?? this.targetScreen,
      data: data ?? this.data,
      isPublic: isPublic ?? this.isPublic,
      targetUserIds: targetUserIds ?? this.targetUserIds,
    );
  }
}
