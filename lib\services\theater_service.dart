import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/theater_model.dart';
import '../utils/role_helper.dart';

class TheaterService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _collection = 'theaters';

  // Helper method to check if current user is admin
  bool _isAdminUser() {
    return RoleHelper.hasAdminAccess();
  }

  // Get all theaters
  Future<List<TheaterModel>> getAllTheaters({bool activeOnly = true}) async {
    try {
      Query query = _firestore.collection(_collection);

      // For non-admin users, always filter to active theaters only
      if (activeOnly || !_isAdminUser()) {
        query = query.where('isActive', isEqualTo: true);
      }

      final snapshot = await query.orderBy('name').get();

      return snapshot.docs
          .map((doc) => TheaterModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get theaters: $e');
    }
  }

  // Get theaters by city
  Future<List<TheaterModel>> getTheatersByCity(String city,
      {bool activeOnly = true}) async {
    try {
      Query query = _firestore.collection(_collection);

      // For non-admin users, always filter to active theaters only
      if (activeOnly || !_isAdminUser()) {
        query = query.where('isActive', isEqualTo: true);
      }

      final snapshot = await query
          .where('address.city', isEqualTo: city)
          .orderBy('name')
          .get();

      return snapshot.docs
          .map((doc) => TheaterModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get theaters by city: $e');
    }
  }

  // Get theater by ID
  Future<TheaterModel?> getTheaterById(String theaterId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(theaterId).get();

      if (doc.exists) {
        return TheaterModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get theater: $e');
    }
  }

  // Search theaters
  Future<List<TheaterModel>> searchTheaters(String query,
      {bool activeOnly = true}) async {
    try {
      Query firestoreQuery = _firestore.collection(_collection);

      // For non-admin users, always filter to active theaters only
      if (activeOnly || !_isAdminUser()) {
        firestoreQuery = firestoreQuery.where('isActive', isEqualTo: true);
      }

      final snapshot = await firestoreQuery.get();
      final theaters =
          snapshot.docs.map((doc) => TheaterModel.fromFirestore(doc)).toList();

      // Filter by name or address
      return theaters.where((theater) {
        final name = theater.name.toLowerCase();
        final address = theater.address.fullAddress.toLowerCase();
        final searchQuery = query.toLowerCase();

        return name.contains(searchQuery) || address.contains(searchQuery);
      }).toList();
    } catch (e) {
      throw Exception('Failed to search theaters: $e');
    }
  }

  // Get theaters with specific facilities
  Future<List<TheaterModel>> getTheatersWithFacilities(List<String> facilities,
      {bool activeOnly = true}) async {
    try {
      Query query = _firestore.collection(_collection);

      // For non-admin users, always filter to active theaters only
      if (activeOnly || !_isAdminUser()) {
        query = query.where('isActive', isEqualTo: true);
      }

      final snapshot =
          await query.where('facilities', arrayContainsAny: facilities).get();

      return snapshot.docs
          .map((doc) => TheaterModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get theaters with facilities: $e');
    }
  }

  // Get theaters near location (simplified distance calculation)
  Future<List<TheaterModel>> getTheatersNearLocation(
      double latitude, double longitude,
      {double radiusKm = 10.0}) async {
    try {
      final allTheaters = await getAllTheaters();

      return allTheaters.where((theater) {
        final distance = theater.distanceFrom(latitude, longitude);
        return distance <= radiusKm;
      }).toList()
        ..sort((a, b) => a
            .distanceFrom(latitude, longitude)
            .compareTo(b.distanceFrom(latitude, longitude)));
    } catch (e) {
      throw Exception('Failed to get nearby theaters: $e');
    }
  }

  // Admin functions
  Future<TheaterModel> addTheater(TheaterModel theater) async {
    try {
      final now = DateTime.now();
      final theaterData = theater.copyWith(
        createdAt: now,
        updatedAt: now,
      );

      final docRef = await _firestore
          .collection(_collection)
          .add(theaterData.toFirestore());

      return theaterData.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('Failed to add theater: $e');
    }
  }

  Future<void> updateTheater(TheaterModel theater) async {
    try {
      final updatedTheater = theater.copyWith(updatedAt: DateTime.now());

      await _firestore
          .collection(_collection)
          .doc(theater.id)
          .update(updatedTheater.toFirestore());
    } catch (e) {
      throw Exception('Failed to update theater: $e');
    }
  }

  Future<void> deleteTheater(String theaterId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(theaterId)
          .update({'isActive': false, 'updatedAt': Timestamp.now()});
    } catch (e) {
      throw Exception('Failed to delete theater: $e');
    }
  }

  // Stream for real-time updates
  Stream<List<TheaterModel>> getTheatersStream({bool activeOnly = true}) {
    Query query = _firestore.collection(_collection);

    // For non-admin users, always filter to active theaters only
    if (activeOnly || !_isAdminUser()) {
      query = query.where('isActive', isEqualTo: true);
    }

    return query.orderBy('name').snapshots().map((snapshot) =>
        snapshot.docs.map((doc) => TheaterModel.fromFirestore(doc)).toList());
  }

  Stream<TheaterModel?> getTheaterStream(String theaterId) {
    return _firestore
        .collection(_collection)
        .doc(theaterId)
        .snapshots()
        .map((doc) => doc.exists ? TheaterModel.fromFirestore(doc) : null);
  }

  // Get unique cities
  Future<List<String>> getAvailableCities({bool activeOnly = true}) async {
    try {
      Query query = _firestore.collection(_collection);

      // For non-admin users, always filter to active theaters only
      if (activeOnly || !_isAdminUser()) {
        query = query.where('isActive', isEqualTo: true);
      }

      final snapshot = await query.get();

      final cities = snapshot.docs
          .map((doc) => TheaterModel.fromFirestore(doc).address.city)
          .toSet()
          .toList();

      cities.sort();
      return cities;
    } catch (e) {
      throw Exception('Failed to get available cities: $e');
    }
  }

  // Get available facilities
  Future<List<String>> getAvailableFacilities({bool activeOnly = true}) async {
    try {
      Query query = _firestore.collection(_collection);

      // For non-admin users, always filter to active theaters only
      if (activeOnly || !_isAdminUser()) {
        query = query.where('isActive', isEqualTo: true);
      }

      final snapshot = await query.get();

      final facilities = <String>{};
      for (final doc in snapshot.docs) {
        final theater = TheaterModel.fromFirestore(doc);
        facilities.addAll(theater.facilities);
      }

      final facilitiesList = facilities.toList();
      facilitiesList.sort();
      return facilitiesList;
    } catch (e) {
      throw Exception('Failed to get available facilities: $e');
    }
  }

  // Statistics
  Future<Map<String, dynamic>> getTheaterStatistics() async {
    try {
      final snapshot = await _firestore.collection(_collection).get();

      int totalTheaters = 0;
      int activeTheaters = 0;
      final cityCounts = <String, int>{};
      final facilityCounts = <String, int>{};

      for (final doc in snapshot.docs) {
        final theater = TheaterModel.fromFirestore(doc);
        totalTheaters++;

        if (theater.isActive) {
          activeTheaters++;

          // Count by city
          final city = theater.address.city;
          cityCounts[city] = (cityCounts[city] ?? 0) + 1;

          // Count facilities
          for (final facility in theater.facilities) {
            facilityCounts[facility] = (facilityCounts[facility] ?? 0) + 1;
          }
        }
      }

      return {
        'totalTheaters': totalTheaters,
        'activeTheaters': activeTheaters,
        'inactiveTheaters': totalTheaters - activeTheaters,
        'cityCounts': cityCounts,
        'facilityCounts': facilityCounts,
      };
    } catch (e) {
      throw Exception('Failed to get theater statistics: $e');
    }
  }
}
