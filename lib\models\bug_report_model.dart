import 'package:cloud_firestore/cloud_firestore.dart';

enum BugStatus {
  pending,    // Chưa nhận
  accepted,   // Đã nhận
  inProgress, // Đang fix
  fixed       // Đã fix
}

extension BugStatusExtension on BugStatus {
  String get name {
    switch (this) {
      case BugStatus.pending:
        return 'pending';
      case BugStatus.accepted:
        return 'accepted';
      case BugStatus.inProgress:
        return 'inProgress';
      case BugStatus.fixed:
        return 'fixed';
    }
  }

  String get displayName {
    switch (this) {
      case BugStatus.pending:
        return 'Chưa nhận';
      case BugStatus.accepted:
        return 'Đã nhận';
      case BugStatus.inProgress:
        return 'Đang fix';
      case BugStatus.fixed:
        return 'Đã fix';
    }
  }

  static BugStatus fromString(String? value) {
    if (value == 'accepted') return BugStatus.accepted;
    if (value == 'inProgress') return BugStatus.inProgress;
    if (value == 'fixed') return BugStatus.fixed;
    return BugStatus.pending; // Default status
  }
}

class BugReportModel {
  final String id;
  final String title;
  final String description;
  final String reportedBy; // User ID
  final String reportedByName;
  final String? reportedByEmail;
  final DateTime createdAt;
  final BugStatus status;
  final String? assignedTo; // Developer ID
  final String? assignedToName;
  final List<BugResponseModel> responses;
  final Map<String, dynamic>? additionalData;

  BugReportModel({
    required this.id,
    required this.title,
    required this.description,
    required this.reportedBy,
    required this.reportedByName,
    this.reportedByEmail,
    required this.createdAt,
    this.status = BugStatus.pending,
    this.assignedTo,
    this.assignedToName,
    this.responses = const [],
    this.additionalData,
  });

  factory BugReportModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    // Chuyển đổi danh sách responses từ Firestore
    List<BugResponseModel> responsesList = [];
    if (data['responses'] != null) {
      final List<dynamic> responsesData = data['responses'];
      responsesList = responsesData
          .map((response) => BugResponseModel.fromMap(response))
          .toList();
    }

    return BugReportModel(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      reportedBy: data['reportedBy'] ?? '',
      reportedByName: data['reportedByName'] ?? 'Unknown User',
      reportedByEmail: data['reportedByEmail'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      status: BugStatusExtension.fromString(data['status']),
      assignedTo: data['assignedTo'],
      assignedToName: data['assignedToName'],
      responses: responsesList,
      additionalData: data['additionalData'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'reportedBy': reportedBy,
      'reportedByName': reportedByName,
      'reportedByEmail': reportedByEmail,
      'createdAt': Timestamp.fromDate(createdAt),
      'status': status.name,
      'assignedTo': assignedTo,
      'assignedToName': assignedToName,
      'responses': responses.map((response) => response.toMap()).toList(),
      'additionalData': additionalData,
    };
  }

  BugReportModel copyWith({
    String? id,
    String? title,
    String? description,
    String? reportedBy,
    String? reportedByName,
    String? reportedByEmail,
    DateTime? createdAt,
    BugStatus? status,
    String? assignedTo,
    String? assignedToName,
    List<BugResponseModel>? responses,
    Map<String, dynamic>? additionalData,
  }) {
    return BugReportModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      reportedBy: reportedBy ?? this.reportedBy,
      reportedByName: reportedByName ?? this.reportedByName,
      reportedByEmail: reportedByEmail ?? this.reportedByEmail,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      assignedTo: assignedTo ?? this.assignedTo,
      assignedToName: assignedToName ?? this.assignedToName,
      responses: responses ?? this.responses,
      additionalData: additionalData ?? this.additionalData,
    );
  }
}

class BugResponseModel {
  final String id;
  final String responderId; // User ID
  final String responderName;
  final String message;
  final DateTime createdAt;
  final bool isFromDeveloper;
  final bool isFromAdmin;
  final BugStatus? newStatus;

  BugResponseModel({
    required this.id,
    required this.responderId,
    required this.responderName,
    required this.message,
    required this.createdAt,
    this.isFromDeveloper = false,
    this.isFromAdmin = false,
    this.newStatus,
  });

  factory BugResponseModel.fromMap(Map<String, dynamic> data) {
    return BugResponseModel(
      id: data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      responderId: data['responderId'] ?? '',
      responderName: data['responderName'] ?? 'Unknown',
      message: data['message'] ?? '',
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      isFromDeveloper: data['isFromDeveloper'] ?? false,
      isFromAdmin: data['isFromAdmin'] ?? false,
      newStatus: data['newStatus'] != null
          ? BugStatusExtension.fromString(data['newStatus'])
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'responderId': responderId,
      'responderName': responderName,
      'message': message,
      'createdAt': Timestamp.fromDate(createdAt),
      'isFromDeveloper': isFromDeveloper,
      'isFromAdmin': isFromAdmin,
      'newStatus': newStatus?.name,
    };
  }
}
