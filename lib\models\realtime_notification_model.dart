class RealtimeNotificationModel {
  final String id;
  final String title;
  final String body;
  final String? imageUrl;
  final int createdAt;
  final int expiresAt;
  final String? targetScreen;
  final Map<String, dynamic>? data;
  final bool isPublic;
  final List<String>? targetUserIds;

  RealtimeNotificationModel({
    required this.id,
    required this.title,
    required this.body,
    this.imageUrl,
    required this.createdAt,
    required this.expiresAt,
    this.targetScreen,
    this.data,
    required this.isPublic,
    this.targetUserIds,
  });

  factory RealtimeNotificationModel.fromJson(String id, Map<String, dynamic> json) {
    return RealtimeNotificationModel(
      id: id,
      title: json['title'] ?? '',
      body: json['body'] ?? '',
      imageUrl: json['imageUrl'],
      createdAt: json['createdAt'] ?? 0,
      expiresAt: json['expiresAt'] ?? 0,
      targetScreen: json['targetScreen'],
      data: json['data'],
      isPublic: json['isPublic'] ?? true,
      targetUserIds: json['targetUserIds'] != null
          ? List<String>.from(json['targetUserIds'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'body': body,
      'imageUrl': imageUrl,
      'createdAt': createdAt,
      'expiresAt': expiresAt,
      'targetScreen': targetScreen,
      'data': data,
      'isPublic': isPublic,
      'targetUserIds': targetUserIds,
    };
  }
}

class RealtimeUserNotificationModel {
  final String id;
  final String userId;
  final String notificationId;
  final bool isRead;
  final bool isSeen;
  final bool isDeleted;
  final int createdAt;
  final int? readAt;
  final int? seenAt;
  final int? deletedAt;

  RealtimeUserNotificationModel({
    required this.id,
    required this.userId,
    required this.notificationId,
    required this.isRead,
    required this.isSeen,
    required this.isDeleted,
    required this.createdAt,
    this.readAt,
    this.seenAt,
    this.deletedAt,
  });

  factory RealtimeUserNotificationModel.fromJson(String id, Map<String, dynamic> json) {
    return RealtimeUserNotificationModel(
      id: id,
      userId: json['userId'] ?? '',
      notificationId: json['notificationId'] ?? '',
      isRead: json['isRead'] ?? false,
      isSeen: json['isSeen'] ?? false,
      isDeleted: json['isDeleted'] ?? false,
      createdAt: json['createdAt'] ?? 0,
      readAt: json['readAt'],
      seenAt: json['seenAt'],
      deletedAt: json['deletedAt'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'notificationId': notificationId,
      'isRead': isRead,
      'isSeen': isSeen,
      'isDeleted': isDeleted,
      'createdAt': createdAt,
      'readAt': readAt,
      'seenAt': seenAt,
      'deletedAt': deletedAt,
    };
  }
}
