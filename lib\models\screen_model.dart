import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

enum ScreenType { standard, vip, imax, dolby, premium }

extension ScreenTypeExtension on ScreenType {
  String get name {
    switch (this) {
      case ScreenType.standard:
        return 'standard';
      case ScreenType.vip:
        return 'vip';
      case ScreenType.imax:
        return 'imax';
      case ScreenType.dolby:
        return 'dolby';
      case ScreenType.premium:
        return 'premium';
    }
  }

  static ScreenType fromString(String? value) {
    switch (value) {
      case 'vip':
        return ScreenType.vip;
      case 'imax':
        return ScreenType.imax;
      case 'dolby':
        return ScreenType.dolby;
      case 'premium':
        return ScreenType.premium;
      default:
        return ScreenType.standard;
    }
  }

  String get displayName {
    switch (this) {
      case ScreenType.standard:
        return 'Phòng Thường';
      case ScreenType.vip:
        return 'Phòng VIP';
      case ScreenType.imax:
        return 'IMAX';
      case ScreenType.dolby:
        return 'Dolby Atmos';
      case ScreenType.premium:
        return 'Premium';
    }
  }

  IconData get icon {
    switch (this) {
      case ScreenType.standard:
        return Icons.tv;
      case ScreenType.vip:
        return Icons.star;
      case ScreenType.imax:
        return Icons.theaters;
      case ScreenType.dolby:
        return Icons.surround_sound;
      case ScreenType.premium:
        return Icons.diamond;
    }
  }

  Color get color {
    switch (this) {
      case ScreenType.standard:
        return Colors.blue;
      case ScreenType.vip:
        return Colors.amber;
      case ScreenType.imax:
        return Colors.red;
      case ScreenType.dolby:
        return Colors.green;
      case ScreenType.premium:
        return Colors.purple;
    }
  }
}

enum SeatType { standard, vip, couple, disabled }

extension SeatTypeExtension on SeatType {
  String get name {
    switch (this) {
      case SeatType.standard:
        return 'standard';
      case SeatType.vip:
        return 'vip';
      case SeatType.couple:
        return 'couple';
      case SeatType.disabled:
        return 'disabled';
    }
  }

  static SeatType fromString(String? value) {
    switch (value) {
      case 'vip':
        return SeatType.vip;
      case 'couple':
        return SeatType.couple;
      case 'disabled':
        return SeatType.disabled;
      default:
        return SeatType.standard;
    }
  }

  String get displayName {
    switch (this) {
      case SeatType.standard:
        return 'Ghế Thường';
      case SeatType.vip:
        return 'Ghế VIP';
      case SeatType.couple:
        return 'Ghế Đôi';
      case SeatType.disabled:
        return 'Ghế Người Khuyết Tật';
    }
  }
}

class SeatModel {
  final String number;
  final SeatType type;
  final bool isAvailable;

  SeatModel({
    required this.number,
    required this.type,
    this.isAvailable = true,
  });

  factory SeatModel.fromJson(Map<String, dynamic> json) {
    return SeatModel(
      number: json['number'] ?? '',
      type: SeatTypeExtension.fromString(json['type']),
      isAvailable: json['isAvailable'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'number': number,
      'type': type.name,
      'isAvailable': isAvailable,
    };
  }

  SeatModel copyWith({
    String? number,
    SeatType? type,
    bool? isAvailable,
  }) {
    return SeatModel(
      number: number ?? this.number,
      type: type ?? this.type,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }

  String get seatId => number;
  String get displayName => '${type.displayName} $number';
}

class SeatRowModel {
  final String row;
  final List<SeatModel> seats;

  SeatRowModel({
    required this.row,
    required this.seats,
  });

  factory SeatRowModel.fromJson(Map<String, dynamic> json) {
    return SeatRowModel(
      row: json['row'] ?? '',
      seats: (json['seats'] as List<dynamic>?)
              ?.map((seat) => SeatModel.fromJson(seat))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'row': row,
      'seats': seats.map((seat) => seat.toJson()).toList(),
    };
  }

  SeatRowModel copyWith({
    String? row,
    List<SeatModel>? seats,
  }) {
    return SeatRowModel(
      row: row ?? this.row,
      seats: seats ?? this.seats,
    );
  }

  int get totalSeats => seats.length;
  int get availableSeats => seats.where((seat) => seat.isAvailable).length;
  List<SeatModel> get availableSeatsList =>
      seats.where((seat) => seat.isAvailable).toList();

  SeatModel? getSeat(String seatNumber) {
    try {
      return seats.firstWhere((seat) => seat.number == seatNumber);
    } catch (e) {
      return null;
    }
  }
}

class ScreenModel {
  final String id;
  final String theaterId;
  final String name;
  final ScreenType type;
  final int totalSeats;
  final int rows;
  final int seatsPerRow;
  final List<SeatRowModel> seatLayout;
  final List<String>
      amenities; // "air_conditioning", "dolby_atmos", "reclining_seats"
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  ScreenModel({
    required this.id,
    required this.theaterId,
    required this.name,
    required this.type,
    required this.totalSeats,
    required this.rows,
    required this.seatsPerRow,
    required this.seatLayout,
    this.amenities = const [],
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ScreenModel.fromJson(Map<String, dynamic> json) {
    return ScreenModel(
      id: json['id'] ?? '',
      theaterId: json['theaterId'] ?? '',
      name: json['name'] ?? '',
      type: ScreenTypeExtension.fromString(json['type']),
      totalSeats: json['totalSeats'] ?? 0,
      rows: json['rows'] ?? 0,
      seatsPerRow: json['seatsPerRow'] ?? 0,
      seatLayout: (json['seatLayout'] as List<dynamic>?)
              ?.map((row) => SeatRowModel.fromJson(row))
              .toList() ??
          [],
      amenities: List<String>.from(json['amenities'] ?? []),
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? (json['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  factory ScreenModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ScreenModel.fromJson({...data, 'id': doc.id});
  }

  Map<String, dynamic> toJson() {
    return {
      'theaterId': theaterId,
      'name': name,
      'type': type.name,
      'totalSeats': totalSeats,
      'rows': rows,
      'seatsPerRow': seatsPerRow,
      'seatLayout': seatLayout.map((row) => row.toJson()).toList(),
      'amenities': amenities,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  Map<String, dynamic> toFirestore() => toJson();

  ScreenModel copyWith({
    String? id,
    String? theaterId,
    String? name,
    ScreenType? type,
    int? totalSeats,
    int? rows,
    int? seatsPerRow,
    List<SeatRowModel>? seatLayout,
    List<String>? amenities,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ScreenModel(
      id: id ?? this.id,
      theaterId: theaterId ?? this.theaterId,
      name: name ?? this.name,
      type: type ?? this.type,
      totalSeats: totalSeats ?? this.totalSeats,
      rows: rows ?? this.rows,
      seatsPerRow: seatsPerRow ?? this.seatsPerRow,
      seatLayout: seatLayout ?? this.seatLayout,
      amenities: amenities ?? this.amenities,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  int get availableSeats =>
      seatLayout.fold(0, (total, row) => total + row.availableSeats);

  bool get hasAirConditioning => amenities.contains('air_conditioning');
  bool get hasDolbyAtmos => amenities.contains('dolby_atmos');
  bool get hasRecliningSeats => amenities.contains('reclining_seats');

  SeatModel? getSeat(String rowLetter, String seatNumber) {
    // ignore: sdk_version_since
    final row = seatLayout.where((r) => r.row == rowLetter).firstOrNull;
    return row?.getSeat(seatNumber);
  }

  List<SeatModel> getAllAvailableSeats() {
    List<SeatModel> allSeats = [];
    for (var row in seatLayout) {
      allSeats.addAll(row.availableSeatsList);
    }
    return allSeats;
  }

  String get displayName => '$name (${type.displayName})';
}
