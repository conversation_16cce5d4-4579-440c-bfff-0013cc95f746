import 'package:cloud_firestore/cloud_firestore.dart';

enum MovieStatus {
  nowPlaying,
  upcoming,
  ended,
}

extension MovieStatusExtension on MovieStatus {
  String get displayName {
    switch (this) {
      case MovieStatus.nowPlaying:
        return '<PERSON><PERSON> chiếu';
      case MovieStatus.upcoming:
        return 'Sắp chiếu';
      case MovieStatus.ended:
        return 'Đã kết thúc';
    }
  }

  static MovieStatus fromString(String? status) {
    switch (status) {
      case 'nowPlaying':
        return MovieStatus.nowPlaying;
      case 'upcoming':
        return MovieStatus.upcoming;
      case 'ended':
        return MovieStatus.ended;
      default:
        return MovieStatus.nowPlaying;
    }
  }
}

class Movie {
  final int id;
  final String title;
  final String? originalTitle;
  final String? subtitle;
  final String? overview;
  final String? posterPath;
  final String? backdropPath;
  final String? trailerUrl;
  final List<String> genres;
  final String? releaseDate;
  final int? runtime;
  final double? voteAverage;
  final int? voteCount;
  final double? popularity;
  final String? ageRating;
  final String? language;
  final String? country;
  final String? director;
  final List<Cast>? cast;
  final MovieStatus status;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isFavorite;
  final bool isHomeBanner;
  final bool isSplashBanner;
  final int? bannerOrder;

  const Movie({
    required this.id,
    required this.title,
    this.originalTitle,
    this.subtitle,
    this.overview,
    this.posterPath,
    this.backdropPath,
    this.trailerUrl,
    this.genres = const [],
    this.releaseDate,
    this.runtime,
    this.voteAverage,
    this.voteCount,
    this.popularity,
    this.ageRating,
    this.language,
    this.country,
    this.director,
    this.cast,
    this.status = MovieStatus.nowPlaying,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.isFavorite = false,
    this.isHomeBanner = false,
    this.isSplashBanner = false,
    this.bannerOrder,
  });

  // Helper methods for parsing
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }

  String get year => releaseDate != null && releaseDate!.length >= 4
      ? releaseDate!.substring(0, 4)
      : '';

  factory Movie.fromJson(Map<String, dynamic> json, {bool isFavorite = false}) {
    try {
      List<String> genresList = [];
      if (json['genres'] != null) {
        try {
          final genresData = json['genres'] as List;
          if (genresData.isNotEmpty) {
            if (genresData.first is String) {
              // Genres are already strings (from Firestore)
              genresList = genresData.cast<String>();
            } else if (genresData.first is Map) {
              // Genres are objects with 'name' property (from TMDB API)
              genresList =
                  genresData.map((genre) => genre['name'] as String).toList();
            }
          }
        } catch (e) {
          print('Error parsing genres: $e');
          genresList = [];
        }
      }

      List<Cast>? castList;
      if (json['credits'] != null && json['credits']['cast'] != null) {
        try {
          castList = (json['credits']['cast'] as List)
              .map((cast) => Cast.fromJson(cast))
              .toList();
        } catch (e) {
          castList = null;
        }
      } else if (json['cast'] != null) {
        try {
          castList = (json['cast'] as List)
              .map((cast) => Cast.fromJson(cast))
              .toList();
        } catch (e) {
          castList = null;
        }
      }

      // Handle different ID types
      int movieId = 0;
      if (json['id'] != null) {
        if (json['id'] is int) {
          movieId = json['id'];
        } else if (json['id'] is String) {
          movieId = int.tryParse(json['id']) ?? json['id'].hashCode.abs();
        }
      }

      return Movie(
        id: movieId,
        title: json['title'] ?? json['name'] ?? 'Unknown',
        originalTitle: json['original_title'] ?? json['originalTitle'],
        subtitle: json['subtitle'] ?? json['tagline'],
        overview: json['overview'],
        posterPath: json['poster_path'] ?? json['posterPath'],
        backdropPath: json['backdrop_path'] ?? json['backdropPath'],
        trailerUrl: json['trailerUrl'] ?? json['trailer_url'],
        genres: genresList,
        releaseDate: json['release_date'] ?? json['releaseDate'],
        runtime: _parseInt(json['runtime']),
        voteAverage: _parseDouble(json['vote_average']) ??
            _parseDouble(json['voteAverage']),
        voteCount:
            _parseInt(json['vote_count']) ?? _parseInt(json['voteCount']),
        popularity: _parseDouble(json['popularity']),
        ageRating:
            json['age_rating']?.toString() ?? json['ageRating']?.toString(),
        language: json['language'] ?? json['original_language'],
        country: json['country'],
        director: json['director'],
        cast: castList,
        status: MovieStatusExtension.fromString(json['status']),
        isActive: json['isActive'] ?? json['is_active'] ?? true,
        createdAt: json['createdAt'] is Timestamp
            ? (json['createdAt'] as Timestamp).toDate()
            : json['createdAt'] != null
                ? DateTime.tryParse(json['createdAt'].toString())
                : null,
        updatedAt: json['updatedAt'] is Timestamp
            ? (json['updatedAt'] as Timestamp).toDate()
            : json['updatedAt'] != null
                ? DateTime.tryParse(json['updatedAt'].toString())
                : null,
        isFavorite: isFavorite,
        isHomeBanner: json['is_home_banner'] ?? json['isHomeBanner'] ?? false,
        isSplashBanner:
            json['is_splash_banner'] ?? json['isSplashBanner'] ?? false,
        bannerOrder: _parseInt(json['banner_order'] ?? json['bannerOrder']),
      );
    } catch (e) {
      print('Error in Movie.fromJson: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  factory Movie.fromFirestore(DocumentSnapshot doc, {bool isFavorite = false}) {
    try {
      final data = doc.data() as Map<String, dynamic>;

      // Handle ID conversion properly
      int movieId;
      if (data['id'] != null) {
        if (data['id'] is int) {
          movieId = data['id'];
        } else if (data['id'] is String) {
          movieId = int.tryParse(data['id']) ?? data['id'].hashCode.abs();
        } else {
          movieId = doc.id.hashCode.abs();
        }
      } else {
        movieId = doc.id.hashCode.abs();
      }

      // Create a safe copy of data with proper ID
      final safeData = Map<String, dynamic>.from(data);
      safeData['id'] = movieId;

      return Movie.fromJson(safeData, isFavorite: isFavorite);
    } catch (e) {
      print('Error parsing movie from document ${doc.id}: $e');
      print('Document data: ${doc.data()}');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'originalTitle': originalTitle,
      'subtitle': subtitle,
      'overview': overview,
      'posterPath': posterPath,
      'backdropPath': backdropPath,
      'trailerUrl': trailerUrl,
      'genres': genres,
      'releaseDate': releaseDate,
      'runtime': runtime,
      'voteAverage': voteAverage,
      'voteCount': voteCount,
      'popularity': popularity,
      'ageRating': ageRating,
      'language': language,
      'country': country,
      'director': director,
      'cast': cast?.map((c) => c.toJson()).toList(),
      'status': status.name,
      'isActive': isActive,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'isFavorite': isFavorite,
      'isHomeBanner': isHomeBanner,
      'isSplashBanner': isSplashBanner,
      'bannerOrder': bannerOrder,

      // Legacy fields for backward compatibility
      'poster_path': posterPath,
      'backdrop_path': backdropPath,
      'release_date': releaseDate,
      'vote_average': voteAverage,
      'age_rating': ageRating,
      'is_favorite': isFavorite,
      'is_home_banner': isHomeBanner,
      'is_splash_banner': isSplashBanner,
      'banner_order': bannerOrder,
    };
  }

  Map<String, dynamic> toFirestore() => toJson();

  Movie copyWith({
    int? id,
    String? title,
    String? originalTitle,
    String? subtitle,
    String? overview,
    String? posterPath,
    String? backdropPath,
    String? trailerUrl,
    List<String>? genres,
    String? releaseDate,
    int? runtime,
    double? voteAverage,
    int? voteCount,
    double? popularity,
    String? ageRating,
    String? language,
    String? country,
    String? director,
    List<Cast>? cast,
    MovieStatus? status,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isFavorite,
    bool? isHomeBanner,
    bool? isSplashBanner,
    int? bannerOrder,
  }) {
    return Movie(
      id: id ?? this.id,
      title: title ?? this.title,
      originalTitle: originalTitle ?? this.originalTitle,
      subtitle: subtitle ?? this.subtitle,
      overview: overview ?? this.overview,
      posterPath: posterPath ?? this.posterPath,
      backdropPath: backdropPath ?? this.backdropPath,
      trailerUrl: trailerUrl ?? this.trailerUrl,
      genres: genres ?? this.genres,
      releaseDate: releaseDate ?? this.releaseDate,
      runtime: runtime ?? this.runtime,
      voteAverage: voteAverage ?? this.voteAverage,
      voteCount: voteCount ?? this.voteCount,
      popularity: popularity ?? this.popularity,
      ageRating: ageRating ?? this.ageRating,
      language: language ?? this.language,
      country: country ?? this.country,
      director: director ?? this.director,
      cast: cast ?? this.cast,
      status: status ?? this.status,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isFavorite: isFavorite ?? this.isFavorite,
      isHomeBanner: isHomeBanner ?? this.isHomeBanner,
      isSplashBanner: isSplashBanner ?? this.isSplashBanner,
      bannerOrder: bannerOrder ?? this.bannerOrder,
    );
  }

  // Helper methods
  bool get isNowPlaying => status == MovieStatus.nowPlaying;
  bool get isUpcoming => status == MovieStatus.upcoming;
  bool get isEnded => status == MovieStatus.ended;

  String get displayStatus => status.displayName;
  String get displayRuntime => runtime != null ? '${runtime}p' : 'N/A';

  bool get hasTrailer => trailerUrl != null && trailerUrl!.isNotEmpty;
  bool get hasDirector => director != null && director!.isNotEmpty;
  bool get hasCast => cast != null && cast!.isNotEmpty;

  double get ratingPercentage =>
      voteAverage != null ? (voteAverage! * 10) : 0.0;

  String get formattedReleaseDate {
    if (releaseDate == null || releaseDate!.isEmpty) return 'N/A';
    try {
      final date = DateTime.parse(releaseDate!);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return releaseDate!;
    }
  }

  bool get isHighRated => voteAverage != null && voteAverage! >= 7.0;
  bool get isPopular => popularity != null && popularity! > 100.0;

  // Image path getters
  String get fullPosterPath {
    if (posterPath == null || posterPath!.isEmpty) {
      return 'https://via.placeholder.com/500x750?text=No+Image';
    }
    if (posterPath!.startsWith('http')) {
      return posterPath!;
    }
    return 'https://image.tmdb.org/t/p/w500$posterPath';
  }

  String get fullBackdropPath {
    if (backdropPath == null || backdropPath!.isEmpty) {
      return 'https://via.placeholder.com/1280x720?text=No+Image';
    }
    if (backdropPath!.startsWith('http')) {
      return backdropPath!;
    }
    return 'https://image.tmdb.org/t/p/w1280$backdropPath';
  }

  // Rating getter for compatibility
  String get rating =>
      voteAverage != null ? voteAverage!.toStringAsFixed(1) : 'N/A';
}

class Cast {
  final int id;
  final String name;
  final String character;
  final String? profilePath;

  const Cast({
    required this.id,
    required this.name,
    required this.character,
    this.profilePath,
  });

  factory Cast.fromJson(Map<String, dynamic> json) {
    return Cast(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      character: json['character'] ?? '',
      profilePath: json['profile_path'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'character': character,
      'profile_path': profilePath,
    };
  }

  // Profile path getter
  String get fullProfilePath {
    if (profilePath == null || profilePath!.isEmpty) {
      return 'https://via.placeholder.com/200x300?text=No+Image';
    }
    if (profilePath!.startsWith('http')) {
      return profilePath!;
    }
    return 'https://image.tmdb.org/t/p/w200$profilePath';
  }
}
